// Copyright 2022 The Sutra Authors. All rights reserved.

/*
Package cloudengine 提供了云端对接规则引擎的实现，用于完成产品识别。

通过 NewClient 创建调用的客户端
	c := cloudengine.NewClient(cloudengine.WithServer("http://*******"))
	// 也可以通过各种With前缀的方法来进行开关配置
	c := cloudengine.NewClient(cloudengine.WithServer(c.server),
		cloudengine.WithQueryRawJson(c.openQueryJson),
		cloudengine.WithDebug(c.openDebug),
		cloudengine.WithOpenGzip(c.openGzip))

通过 ProductsOfHashes 来批量识别产品：
	products, err := c.ProductsOfHashes(hashes)

通过 Products 来识别产品：
	products, err := c.Products(obj)
*/
package cloudengine
