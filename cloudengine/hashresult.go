package cloudengine

import (
	"git.gobies.org/sutra/gosutra/structs"
	"sync"
)

// hashResults 产品获取回调的时候用到的结构
type HashResults struct {
	res sync.Map
}

// EachHash 遍历
func (hr *HashResults) EachHash(f func(ehash string, products []*structs.Product) bool) {
	hr.res.Range(func(key, value interface{}) bool {
		return f(key.(string), value.([]*structs.Product))
	})
}

// Get 根据ehash获取产品列表
func (hr *HashResults) Get(ehash string) []*structs.Product {
	v, _ := hr.res.Load(ehash)
	if v == nil {
		return nil
	}
	return v.([]*structs.Product)
}

// Add 添加产品信息
func (hr *HashResults) Add(ehash string, projects []*structs.Product) bool {
	_, ok := hr.res.LoadOrStore(ehash, projects)
	return !ok
}

// ToArray 转换成数组
func (hr *HashResults) ToArray() []*structs.Product {
	var ps []*structs.Product
	hr.res.Range(func(key, value interface{}) bool {
		for _, p := range value.([]*structs.Product) {
			ps = append(ps, p)
		}
		return true
	})
	return ps
}

// Names 取所有产品名称
func (hr *HashResults) Names() []string {
	var products []string
	hr.res.Range(func(key, value interface{}) bool {
		for _, p := range value.([]*structs.Product) {
			products = append(products, p.Name)
		}
		return true
	})
	return products
}
