package genmap

import (
	"bufio"
	"encoding/json"
	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/algo"
	"git.gobies.org/sutra/gosutra/structs"
	wappalyzer "github.com/projectdiscovery/wappalyzergo"
	"log"
	"net/http"
	"net/textproto"
	"strings"
	"time"
)

func headParse(h string) http.Header {
	// don't forget to make certain the headers end with a second "\r\n"
	reader := bufio.NewReader(strings.NewReader(h + "\r\n"))
	tp := textproto.NewReader(reader)

	_, err := tp.ReadLine()
	if err != nil {
		log.Println(err)
		return nil
	}

	mimeHeader, err := tp.ReadMIMEHeader()
	if err != nil {
		log.Println(err)
		return nil
	}

	// http.Header and textproto.MIMEHeader are both just a map[string][]string
	httpHeader := http.Header(mimeHeader)
	return httpHeader
}

func GenProductsInfoFromMap(httpInfo map[string]interface{},
	sutraCli *gosutra.Client, wappalyzerClient *wappalyzer.Wappalyze, s time.Time) []byte {

	var products []string
	// 先看有没有已经设置的值
	if v, ok := httpInfo["products"]; ok {
		if ps, ok := v.([]string); ok {
			products = ps
		}
	}

	d, err := json.Marshal(httpInfo)
	if err != nil {
		log.Println("[WARNING] json failed:", err)
		return nil
	}
	//log.Println(h.ident, string(d))

	obj, err := structs.NewJsonObj(string(d))
	if err != nil {
		log.Println("[WARNING] NewJsonObj failed:", err)
		return nil
	}
	ps, err := sutraCli.ProductsObj(obj)
	if err != nil {
		log.Println("[WARNING] ProductsOfHashes failed:", err)
		return nil
	}
	//var productStr string

	for i := range ps {
		products = append(products, ps[i].Name)
	}

	if wappalyzerClient != nil {
		var header http.Header
		if v, ok := httpInfo["raw_header"]; ok {
			header = v.(http.Header)
		} else {
			headParse(httpInfo["header"].(string))
		}

		var body []byte
		if httpInfo["body"] != nil {
			body = []byte(httpInfo["body"].(string))
		}
		var fps []string
		for k, _ := range wappalyzerClient.Fingerprint(header, body) {
			fps = append(fps, k)
		}
		httpInfo["wappalyzer_products"] = fps
	}

	httpInfo["products"] = products
	httpInfo["ehash"] = obj.EHash()
	httpInfo["fohash"] = algo.EncryptStringToBase64(obj.NHash(), "fofafofa")
	httpInfo["cost"] = time.Since(s).String()
	delete(httpInfo, "body")
	delete(httpInfo, "raw_header")

	d, err = json.Marshal(httpInfo)
	if err != nil {
		log.Println("[WARNING] json failed:", err)
		return nil
	}
	return d
}
