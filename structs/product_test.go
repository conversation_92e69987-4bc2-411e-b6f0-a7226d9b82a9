package structs

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestNewProductFromLine(t *testing.T) {
	p, err := NewProductFromLine(`	{
		"rule_id":         "1",
		"level":           "2",
		"product":         "test",
		"category":        "cat",
		"parent_category": "pcat",
		"softhard":        "2",
		"company":         "testcompany"
	}`)
	assert.<PERSON>l(t, err)
	assert.NotNil(t, p)
	assert.Equal(t, "2", p.Level)
	assert.Equal(t, "1", p.RuleId)
	assert.Equal(t, "test", p.Name)
	assert.Equal(t, "cat", p.Category)
	assert.Equal(t, "pcat", p.ParentCategory)
	assert.Equal(t, "2", p.Soft<PERSON>ard)
	assert.Equal(t, "testcompany", p.Company)

	p = NewProductFromMap(map[string]string{
		"rule_id":         "2",
		"level":           "2",
		"product":         "test2",
		"category":        "cat2",
		"parent_category": "pcat2",
		"softhard":        "2",
		"company":         "testcompany2",
	})
	assert.<PERSON>l(t, err)
	assert.NotNil(t, p)
	assert.Equal(t, "2", p.Level)
	assert.Equal(t, "2", p.RuleId)
	assert.Equal(t, "test2", p.Name)
	assert.Equal(t, "cat2", p.Category)
	assert.Equal(t, "pcat2", p.ParentCategory)
	assert.Equal(t, "2", p.SoftHard)
	assert.Equal(t, "testcompany2", p.Company)

	p = NewProductFromMap(map[string]string{
		"rule_id":         "2",
		"level":           "2",
		"product":         "test2",
		"category":        "cat2",
		"parent_category": "pcat2",
		"softhard":        "2",
		"company":         "testcompany2",
		"rule":            "body=test",
	})
	assert.Nil(t, err)
	assert.NotNil(t, p)
	assert.Equal(t, "body=test", p.Rule)
	assert.Equal(t, "", p.Copy().Rule)
}
