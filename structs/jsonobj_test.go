package structs

import (
	"strings"
	"github.com/stretchr/testify/assert"
	"github.com/valyala/fastjson"
	"testing"
)

func TestNewJsonObj(t *testing.T) {
	obj, err := NewJsonObj("")
	assert.NotNil(t, err)
	assert.Nil(t, obj)

	obj, err = NewJsonObj(`{"a":"b", "banner":"test-banner"}`)
	assert.Nil(t, err)
	assert.Equal(t, "b", obj.GetString("a"))

	// fastjson不支持
	//obj, err = NewJsonObj(`{"a":{"b":"c"}, "banner":""}`)
	//assert.Nil(t, err)
	//assert.Equal(t, "c", string(obj.GetStringBytes("a.b")))

	// 验证签名
	obj, err = NewJsonObj(`{"body":"<html></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "1465825743341333019", obj.<PERSON><PERSON>())
	assert.Equal(t, "-1255511321700480643", obj.<PERSON>Hash())
	assert.Equal(t, "098f80179f580a6d28145ad2cc205a79", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><known></known></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "7956195940639300741", obj.NHash())
	assert.Equal(t, "-7444481993511630114", obj.FHash())
	assert.Equal(t, "abcb0ba9cefefbb3ad25f955e5ed2e05", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><known><black></black></known></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "4074701324998390642", obj.NHash())
	assert.Equal(t, "3772361052347832008", obj.FHash())
	assert.Equal(t, "f8f64cfbbbbac1db3b5939c27425675f", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><unknown></unknown></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "-2864212857490850327", obj.NHash())
	assert.Equal(t, "-6551419606606946569", obj.FHash())
	assert.Equal(t, "b5569dd36789b7be89cad28bc3c8a495", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><unknown><myapp/></unknown></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "5893961949806089791", obj.NHash())
	assert.Equal(t, "8908312994244279890", obj.FHash())
	assert.Equal(t, "97cf192300b888da473be27d5d84abe1", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><myapp></myapp></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "5890507554379951485", obj.NHash())
	assert.Equal(t, "1443961722667255273", obj.FHash())
	assert.Equal(t, "31fd60f56a1dd3da17a586816e2df77f", obj.EHash())

	obj, err = NewJsonObj(`{"body":"<html><head></head><body><myapp><children></children></myapp></body></html>"}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "-5998512438099429752", obj.NHash())
	assert.Equal(t, "282534951678390883", obj.FHash())
	assert.Equal(t, "ede4a241dec55c7f96620edc64eb94ad", obj.EHash())

	// _index 格式测试
	obj, err = NewJsonObj(`{"_type":"subdomain", "_source":{"body":"<html><head></head><body><myapp><children></children></myapp></body></html>"}}`)
	obj.GenHashes()
	assert.Nil(t, err)
	assert.Equal(t, "-5998512438099429752", obj.NHash())
	assert.Equal(t, "282534951678390883", obj.FHash())
	assert.Equal(t, "ede4a241dec55c7f96620edc64eb94ad", obj.EHash())
}

// TestNormalizeMethod 专门测试 normalize() 方法的各种匹配场景
func TestNormalizeMethod(t *testing.T) {
	tests := []struct {
		name           string
		jsonData       string
		expectedType   ObjectType
		expectedError  bool
		expectedFields map[StringObjectType]string
	}{
		{
			name:          "MySQL服务banner识别",
			jsonData:      `{"protocol": "mysql", "banner": "U\\x00\\x00\\x00 5.1.35-analyticdb\\x00\\xf0\\x07\\x00\\x00TUNEgVX2\\x00^L\\x82!\\x02\\x00\\x1b\\x00\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00xlBz3pPNkOl6\\x00mysql_native_password\\x00", "body":""}`,
			expectedType:  OtService,
			expectedError: false,
			expectedFields: map[StringObjectType]string{
				StBanner: "u\\x00\\x00\\x00 5.1.35-analyticdb\\x00\\xf0\\x07\\x00\\x00tunegvx2\\x00^l\\x82!\\x02\\x00\\x1b\\x00\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00xlbz3ppnkol6\\x00mysql_native_password\\x00",
				StBody:   "",
			},
		},
		{
			name:          "只有body的subdomain类型",
			jsonData:      `{"body": "<html><title>Test</title></html>"}`,
			expectedType:  OtSubdomain,
			expectedError: false,
			expectedFields: map[StringObjectType]string{
				StBody: "<html><title>test</title></html>",
			},
		},
		{
			name:          "只有header的subdomain类型",
			jsonData:      `{"header": "HTTP/1.1 200 OK\r\nServer: nginx"}`,
			expectedType:  OtSubdomain,
			expectedError: false,
			expectedFields: map[StringObjectType]string{
				StHeader: "http/1.1 200 ok\r\nserver: nginx",
			},
		},
		{
			name:          "空banner不应该设置为service类型",
			jsonData:      `{"banner": "", "body": "test"}`,
			expectedType:  OtSubdomain,
			expectedError: false,
			expectedFields: map[StringObjectType]string{
				StBody: "test",
			},
		},
		{
			name:          "有非空banner的service类型",
			jsonData:      `{"banner": "SSH-2.0-OpenSSH_7.4"}`,
			expectedType:  OtService,
			expectedError: false,
			expectedFields: map[StringObjectType]string{
				StBanner: "ssh-2.0-openssh_7.4",
			},
		},
		{
			name:          "包含多个字段的复合数据",
			jsonData:      `{"banner": "HTTP/1.1", "cert": "CN=example.com", "title": "Example Site", "server": "Apache/2.4"}`,
			expectedType:  OtService,
			expectedError: false,
			expectedFields: map[StringObjectType]string{
				StBanner: "http/1.1",
				StCert:   "cn=example.com",
				StTitle:  "example site",
				StServer: "apache/2.4",
			},
		},
		{
			name:          "_type为subdomain的外围格式",
			jsonData:      `{"_type": "subdomain", "_source": {"body": "<html>test</html>"}}`,
			expectedType:  OtSubdomain,
			expectedError: false,
			expectedFields: map[StringObjectType]string{
				StBody: "<html>test</html>",
			},
		},
		{
			name:          "_type为service的外围格式",
			jsonData:      `{"_type": "service", "_source": {"banner": "test-banner"}}`,
			expectedType:  OtService,
			expectedError: false,
			expectedFields: map[StringObjectType]string{
				StBanner: "test-banner",
			},
		},
		{
			name:          "未知_type应该返回错误",
			jsonData:      `{"_type": "unknown", "_source": {"banner": "test"}}`,
			expectedType:  OtMixed,
			expectedError: true,
		},
		{
			name:          "既没有识别字段也没有_type应该返回错误",
			jsonData:      `{"protocol": "tcp", "port": "80"}`,
			expectedType:  OtMixed,
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 直接创建JsonObj而不通过NewJsonObj，这样可以单独测试normalize方法
			fj, err := fastjson.Parse(tt.jsonData)
			assert.Nil(t, err)

			obj := &JsonObj{
				Value: fj,
				raw:   tt.jsonData,
			}

			// 调用normalize方法
			err = obj.normalize()

			if tt.expectedError {
				assert.NotNil(t, err, "期望返回错误但没有返回")
			} else {
				assert.Nil(t, err, "不期望返回错误但返回了: %v", err)
				assert.Equal(t, tt.expectedType, obj.t, "对象类型不匹配")
				assert.True(t, obj.normalized, "normalized标志应该为true")

				// 检查字符串字段是否正确设置
				for fieldType, expectedValue := range tt.expectedFields {
					actualValue := obj.stringValues[fieldType]
					assert.Equal(t, expectedValue, actualValue, "字段 %d 的值不匹配", fieldType)
				}
			}
		})
	}
}

// TestNormalizeIdempotent 测试normalize方法的幂等性
func TestNormalizeIdempotent(t *testing.T) {
	jsonData := `{"banner": "test-banner", "title": "Test Title"}`

	fj, err := fastjson.Parse(jsonData)
	assert.Nil(t, err)

	obj := &JsonObj{
		Value: fj,
		raw:   jsonData,
	}

	// 第一次调用normalize
	err1 := obj.normalize()
	assert.Nil(t, err1)
	assert.True(t, obj.normalized)
	assert.Equal(t, ObjectType(OtService), obj.t)

	firstBanner := obj.stringValues[StBanner]
	firstTitle := obj.stringValues[StTitle]

	// 第二次调用normalize应该直接返回，不做任何处理
	err2 := obj.normalize()
	assert.Nil(t, err2)
	assert.True(t, obj.normalized)
	assert.Equal(t, ObjectType(OtService), obj.t)
	assert.Equal(t, firstBanner, obj.stringValues[StBanner])
	assert.Equal(t, firstTitle, obj.stringValues[StTitle])
}

// TestBannerDebug 专门调试banner字段的问题
func TestBannerDebug(t *testing.T) {
	// 你提供的原始数据
	jsonData := `{
    "protocol": "mysql",
    "banner": "U\x00\x00\x00 5.1.35-analyticdb\x00\xf0\x07\x00\x00TUNEgVX2\x00^L\x82!\x02\x00\x1b\x00\x15\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00xlBz3pPNkOl6\x00mysql_native_password\x00",
    "body":""
}`

	t.Logf("原始JSON数据: %s", jsonData)

	// 解析JSON
	fj, err := fastjson.Parse(jsonData)
	assert.Nil(t, err)

	obj := &JsonObj{
		Value: fj,
		raw:   jsonData,
	}

	// 检查banner字段
	bannerValue := obj.Get("banner")
	t.Logf("banner字段是否存在: %v", bannerValue.Exists())

	if bannerValue.Exists() {
		bannerBytes := bannerValue.GetStringBytes()
		bannerString := string(bannerBytes)
		t.Logf("banner字节长度: %d", len(bannerBytes))
		t.Logf("banner字符串长度: %d", len(bannerString))
		t.Logf("banner字符串内容: %q", bannerString)
		t.Logf("banner是否为空字符串: %v", bannerString == "")
		t.Logf("banner是否满足条件: %v", bannerValue.Exists() && bannerString != "")
	}

	// 调用normalize并检查结果
	err = obj.normalize()
	t.Logf("normalize错误: %v", err)
	t.Logf("对象类型: %v", obj.t)
	t.Logf("banner字段值: %q", obj.stringValues[StBanner])
}

// TestBannerEdgeCases 测试可能导致banner不进入if的边界情况
func TestBannerEdgeCases(t *testing.T) {
	testCases := []struct {
		name     string
		jsonData string
		shouldEnterIf bool
	}{
		{
			name:     "正常banner",
			jsonData: `{"banner": "test-banner"}`,
			shouldEnterIf: true,
		},
		{
			name:     "空字符串banner",
			jsonData: `{"banner": ""}`,
			shouldEnterIf: false,
		},
		{
			name:     "只有空格的banner",
			jsonData: `{"banner": "   "}`,
			shouldEnterIf: true,
		},
		{
			name:     "null值banner",
			jsonData: `{"banner": null}`,
			shouldEnterIf: false,
		},
		{
			name:     "没有banner字段",
			jsonData: `{"protocol": "tcp"}`,
			shouldEnterIf: false,
		},
		{
			name:     "你的MySQL数据",
			jsonData: `{"protocol": "mysql", "banner": "U\\x00\\x00\\x00 5.1.35-analyticdb\\x00\\xf0\\x07\\x00\\x00TUNEgVX2\\x00^L\\x82!\\x02\\x00\\x1b\\x00\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00xlBz3pPNkOl6\\x00mysql_native_password\\x00", "body":""}`,
			shouldEnterIf: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			fj, err := fastjson.Parse(tc.jsonData)
			if err != nil {
				t.Logf("JSON解析失败: %v", err)
				return
			}

			obj := &JsonObj{
				Value: fj,
				raw:   tc.jsonData,
			}

			bannerValue := obj.Get("banner")
			exists := bannerValue.Exists()
			var bannerString string
			var isEmpty bool

			if exists {
				bannerString = string(bannerValue.GetStringBytes())
				isEmpty = bannerString == ""
			}

			shouldEnter := exists && !isEmpty
			t.Logf("字段存在: %v, 字符串: %q, 为空: %v, 应该进入if: %v, 期望进入: %v",
				exists, bannerString, isEmpty, shouldEnter, tc.shouldEnterIf)

			assert.Equal(t, tc.shouldEnterIf, shouldEnter, "if条件判断不符合期望")

			// 调用normalize验证结果
			err = obj.normalize()
			if tc.shouldEnterIf {
				// 如果应该进入if，检查是否设置为service类型
				if err == nil {
					assert.Equal(t, ObjectType(OtService), obj.t, "应该设置为service类型")
					assert.NotEmpty(t, obj.stringValues[StBanner], "banner字段应该被设置")
				}
			}
		})
	}
}

// TestYourExactData 测试你提供的确切数据
func TestYourExactData(t *testing.T) {
	// 你提供的确切数据
	jsonData := `{
    "protocol": "mysql",
    "banner": "U\x00\x00\x00 5.1.35-analyticdb\x00\xf0\x07\x00\x00TUNEgVX2\x00^L\x82!\x02\x00\x1b\x00\x15\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00xlBz3pPNkOl6\x00mysql_native_password\x00",
"body":""
}`

	t.Logf("测试数据: %s", jsonData)

	// 使用 NewJsonObj 创建对象（这会自动调用 normalize）
	obj, err := NewJsonObj(jsonData)

	t.Logf("NewJsonObj 错误: %v", err)
	if err != nil {
		t.Fatalf("创建 JsonObj 失败: %v", err)
	}

	t.Logf("对象类型: %v (期望: %v)", obj.t, OtService)
	t.Logf("normalized 标志: %v", obj.normalized)
	t.Logf("banner 字段值: %q", obj.stringValues[StBanner])
	t.Logf("body 字段值: %q", obj.stringValues[StBody])

	// 验证结果
	assert.Equal(t, ObjectType(OtService), obj.t, "应该被识别为 Service 类型")
	assert.True(t, obj.normalized, "应该已经归一化")
	assert.NotEmpty(t, obj.stringValues[StBanner], "banner 字段应该被设置")
	assert.Contains(t, obj.stringValues[StBanner], "mysql", "banner 应该包含 mysql（转小写后）")

	// 测试直接调用 normalize 方法
	t.Run("直接测试normalize方法", func(t *testing.T) {
		fj, err := fastjson.Parse(jsonData)
		assert.Nil(t, err)

		obj2 := &JsonObj{
			Value: fj,
			raw:   jsonData,
		}

		// 检查 banner 字段
		bannerValue := obj2.Get("banner")
		t.Logf("banner 字段存在: %v", bannerValue.Exists())
		if bannerValue.Exists() {
			bannerString := string(bannerValue.GetStringBytes())
			t.Logf("banner 字符串长度: %d", len(bannerString))
			t.Logf("banner 是否为空: %v", bannerString == "")
			t.Logf("banner 前50个字符: %q", bannerString[:min(50, len(bannerString))])
		}

		// 调用 normalize
		err = obj2.normalize()
		t.Logf("normalize 错误: %v", err)
		t.Logf("normalize 后类型: %v", obj2.t)

		assert.Nil(t, err, "normalize 不应该返回错误")
		assert.Equal(t, ObjectType(OtService), obj2.t, "应该被识别为 Service 类型")
	})
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// TestAPIDataFlow 测试模拟 API 数据流
func TestAPIDataFlow(t *testing.T) {
	// 模拟你发送的 HTTP 请求体
	httpRequestBody := `{
    "protocol": "mysql",
    "banner": "U\x00\x00\x00 5.1.35-analyticdb\x00\xf0\x07\x00\x00TUNEgVX2\x00^L\x82!\x02\x00\x1b\x00\x15\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00xlBz3pPNkOl6\x00mysql_native_password\x00",
"body":""
}`

	t.Logf("HTTP 请求体: %s", httpRequestBody)

	// 模拟 fastjson 解析
	fj, err := fastjson.Parse(httpRequestBody)
	assert.Nil(t, err, "JSON 解析应该成功")

	// 检查各个字段
	protocolValue := fj.Get("protocol")
	bannerValue := fj.Get("banner")
	bodyValue := fj.Get("body")

	t.Logf("protocol 存在: %v, 值: %q", protocolValue.Exists(), string(protocolValue.GetStringBytes()))
	t.Logf("banner 存在: %v, 长度: %d", bannerValue.Exists(), len(bannerValue.GetStringBytes()))
	t.Logf("body 存在: %v, 值: %q", bodyValue.Exists(), string(bodyValue.GetStringBytes()))

	// 直接使用原始数据创建 JsonObj
	obj, err := NewJsonObj(httpRequestBody)
	assert.Nil(t, err, "应该成功创建 JsonObj")
	assert.Equal(t, ObjectType(OtService), obj.t, "应该识别为 Service 类型")

	t.Logf("最终结果 - 类型: %v, Banner: %q", obj.t, obj.stringValues[StBanner])
}

// TestAnalyticDBMatching 测试 AnalyticDB 规则匹配问题
func TestAnalyticDBMatching(t *testing.T) {
	// 你的实际数据
	jsonData := `{
    "protocol": "mysql",
    "banner": "U\\x00\\x00\\x00 5.1.35-analyticdb\\x00\\xf0\\x07\\x00\\x00TUNEgVX2\\x00^L\\x82!\\x02\\x00\\x1b\\x00\\x15\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00xlBz3pPNkOl6\\x00mysql_native_password\\x00",
    "body": ""
}`

	obj, err := NewJsonObj(jsonData)
	assert.Nil(t, err)

	t.Logf("Banner 原始值: %q", obj.Get("banner").String())
	t.Logf("Banner normalize后: %q", obj.stringValues[StBanner])
	t.Logf("Banner StringOfField: %q", obj.StringOfField("banner"))

	// 模拟规则匹配逻辑
	checkValue := "AnalyticDB"
	checkValueLower := strings.ToLower(checkValue)
	bannerValue := obj.StringOfField("banner")

	t.Logf("规则值: %q", checkValue)
	t.Logf("规则值(小写): %q", checkValueLower)
	t.Logf("数据值: %q", bannerValue)
	t.Logf("包含检查: strings.Contains(%q, %q) = %v", bannerValue, checkValueLower, strings.Contains(bannerValue, checkValueLower))

	// 验证匹配逻辑
	assert.True(t, strings.Contains(bannerValue, checkValueLower), "应该匹配 AnalyticDB")

	// 验证 protocol 匹配
	protocolValue := obj.StringOfField("protocol")
	t.Logf("Protocol 值: %q", protocolValue)
	assert.True(t, strings.Contains(protocolValue, "mysql"), "应该匹配 mysql")

	// 模拟完整规则：(protocol="mysql" && banner="AnalyticDB") || title=="AnalyticDB for MySQL Login"
	condition1 := strings.Contains(protocolValue, "mysql") && strings.Contains(bannerValue, checkValueLower)
	titleValue := obj.StringOfField("title")
	condition2 := titleValue == "analyticdb for mysql login" // 注意：title 用的是 == 精确匹配

	t.Logf("条件1 (protocol=mysql && banner=AnalyticDB): %v", condition1)
	t.Logf("条件2 (title==AnalyticDB for MySQL Login): %v (title: %q)", condition2, titleValue)
	t.Logf("整体规则匹配: %v", condition1 || condition2)

	assert.True(t, condition1 || condition2, "完整规则应该匹配")
}
