// Copyright 2022 The Sutra Authors. All rights reserved.

/*
Package gosutra 提供了产品识别功能

通过 Products 获取所有匹配的产品列表:

	c := gosutra.NewClient(gosutra.WithServer("http://1.1.1.1"))
	v, err := c.Products(jsonStr)

通过 WithCloudQuery 和 WithQueryRawJson 进行云引擎的启用

	c := gosutra.NewClient(
		gosutra.WithCloudQuery(true),
		gosutra.WithQueryRawJson(true),
	)

通过 WithRules 进行自定义规则的加载

	c := gosutra.NewClient(
		gosutra.WithRules(`{"product":"testrule","rule":"banner=abcd"}`, false),
	)
	// 接着就可以通过Products看到效果
	c.Products(`{"banner":"abcd"}`)
	// 也可以直接加载文件
	c := gosutra.NewClient(
		gosutra.WithRuleFile(`/path/to/rules.json`, false),
	)

通过 WithCloudCallback 来开启云引擎的回调（非阻塞模式，处理完成后会回调）

	c := gosutra.NewClient(
		gosutra.WithCloudQuery(true),
		gosutra.WithQueryRawJson(true),
		gosutra.WithCloudCallback(func(ehash string, products []*structs.Product){
			log.Println(ehash, products)
		}),
	)
	v, err := c.Products(jsonStr)
*/
package gosutra

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"log"
	"sync"
	"time"

	"git.gobies.org/sutra/gosutra/cloudengine"
	"git.gobies.org/sutra/gosutra/resources"
	"git.gobies.org/sutra/gosutra/rulengine"
	"git.gobies.org/sutra/gosutra/structs"
	"github.com/bluele/gcache"
)

func LoadInnerRules() {
	s := time.Now()
	defer func() {
		log.Println("load rules costs ", time.Since(s))
	}()

	data, err := resources.ResFs.ReadFile("rules.json")
	if err != nil {
		panic(err)
	}
	rulengine.Load(context.Background(), string(data), false)

}

func LoadInnerEncryptRules() {
	s := time.Now()
	defer func() {
		log.Println("load rules costs ", time.Since(s))
	}()

	data, err := resources.ResFs.ReadFile("rules.json")
	if err != nil {
		panic(err)
	}

	rulengine.LoadFromEncrypt(context.Background(), string(data), false)
}

type Client struct {
	server                 string                 // 服务器地址
	closeLocalEngine       bool                   // 是否关闭本地殷勤
	openCloudQuery         bool                   // 是否开启云引擎
	openQueryJson          bool                   // 是否开启查询原始json模式
	openGzip               bool                   // 大文件启用gzip
	openDebug              bool                   // 开启调试
	hashCacheMinute        time.Duration          // 云端缓存的时间
	lastWriteFileCacheTime time.Time              // 最后写入文件缓存的时间
	writeFileCacheSecond   time.Duration          // 写入文件缓存的时间最小间隔
	cacheMutex             sync.Mutex             // 写文件锁
	hashCacheFile          string                 // hash本地缓存文件
	cloudCallback          cloudengine.CallbackFn // 开启调试
	cc                     *cloudengine.Client
	once                   sync.Once
	hashCache              gcache.Cache
}

type ClientOption func(*Client)

func NewClient(options ...ClientOption) *Client {
	c := &Client{
		server:               "http://127.0.0.1",
		writeFileCacheSecond: time.Second * 30,
		hashCacheFile:        "rules_cache.txt",
	}
	for _, opt := range options {
		opt(c)
	}

	if c.hashCacheMinute > 0 {
		c.hashCache = gcache.New(1000).
			LFU().
			Build()
		if err := c.loadHashCacheFile(); err != nil {
			log.Printf("[ERROR] cache file load failed. %v", err)
		}

	}

	c.initCC()
	return c
}

// 控制请求服务器地址
func WithServer(server string) ClientOption {
	return func(c *Client) {
		c.server = server
	}
}

// 控制是否开启云引擎
func WithCloseLocalEngine(v bool) ClientOption {
	return func(c *Client) {
		c.closeLocalEngine = v
	}
}

// 控制是否开启云引擎
func WithCloudQuery(v bool) ClientOption {
	return func(c *Client) {
		c.openCloudQuery = v
	}
}

// 控制是否开启查询原始json模式开关
func WithQueryRawJson(v bool) ClientOption {
	return func(c *Client) {
		c.openQueryJson = v
	}
}

// 打开hash本地缓存
func WithHashCacheMinute(v int) ClientOption {
	return func(c *Client) {
		c.hashCacheMinute = time.Minute * time.Duration(v)
	}
}

// 修改缓存的文件名
func WithHashCacheFile(f string) ClientOption {
	return func(c *Client) {
		c.hashCacheFile = f
	}
}

// 控制是否开启gzip模式，大文件模式下可以减少网络传输
func WithOpenGzip(v bool) ClientOption {
	return func(c *Client) {
		c.openGzip = v
	}
}

// 控制是否开启调试
func WithDebug(v bool) ClientOption {
	return func(c *Client) {
		c.openDebug = v
	}
}

// 加载规则内容
func WithRules(ctx context.Context, rules string, clearBefore bool) ClientOption {
	return func(c *Client) {
		rulengine.Load(ctx, rules, clearBefore)
	}
}

// 加载规则文件
func WithRuleFile(ctx context.Context, ruleFile string, clearBefore bool) ClientOption {
	return func(c *Client) {
		rulengine.LoadFromFile(ctx, ruleFile, clearBefore)
	}
}

// 加载规则文件
func WithCloudCallback(fn cloudengine.CallbackFn) ClientOption {
	return func(c *Client) {
		c.cloudCallback = fn
	}
}

// 初始化云引擎
func (c *Client) initCC() {
	c.once.Do(func() {
		if c.cc == nil {
			c.cc = cloudengine.NewClient(cloudengine.WithServer(c.server),
				cloudengine.WithQueryRawJson(c.openQueryJson),
				cloudengine.WithDebug(c.openDebug),
				cloudengine.WithOpenGzip(c.openGzip),
			)
		}
	})
}

/*
searchLocal 通过obj检索本地引擎

obj 查询的jsonObj，从grab中来，分为service和subdomain

tags 返回所有的标签列表，与yara原来的规则对应

err 错误代码
*/
func (c *Client) searchLocal(obj *structs.JsonObj) (products []*structs.Product, err error) {
	return rulengine.Products(obj)
}

/*
searchCloud 通过obj检索云端引擎

jsonContent 查询的jsonObj，从grab中来，分为service和subdomain

tags 返回所有的标签列表，与yara原来的规则对应

err 错误代码
*/
func (c *Client) searchCloud(obj *structs.JsonObj) ([]*structs.Product, error) {
	ps, err := c.cc.Products([]*structs.JsonObj{obj}, c.cloudCallback)
	if ps == nil {
		return nil, err
	}
	return ps.ToArray(), err
}

func (c *Client) cacheEnabled() bool {
	return c.hashCacheMinute > 0
}

func (c *Client) ProductsObj(obj *structs.JsonObj) (products []*structs.Product, err error) {
	blacked := false
	// 先从缓存取
	if c.cacheEnabled() {
		if c.hashCache.Has(obj.EHash()) {
			if v, err := c.hashCache.Get(obj.EHash()); err == nil {
				products = v.([]*structs.Product)

				for i := range products {
					// 不是黑名单就直接返回
					if products[i].Name != cloudengine.BLOCKED {
						return products, nil
					} else {
						blacked = true
					}
				}
			} else {
				log.Println("[WARNING] cache failed:", err)
			}
		}
	}

	if !c.closeLocalEngine {
		products, err = c.searchLocal(obj)
	}

	if c.openCloudQuery {
		// 已经标黑就不再进行查询了
		if blacked {
			return
		}

		var ps []*structs.Product
		ps, err = c.searchCloud(obj)
		if err != nil {
			return
		}

		for i := range ps {
			if ps[i].Name != cloudengine.BLOCKED {
				products = append(products, ps[i])
			}
		}

		c.doCache(obj.EHash(), ps)
	}
	return
}

/*
Products 完成从json字符串到查询产品信息的功能
*/
func (c *Client) Products(jsonContent string) (products []*structs.Product, err error) {
	var obj *structs.JsonObj
	obj, err = structs.NewJsonObj(jsonContent)
	if err != nil {
		return nil, err
	}
	obj.GenHashes()

	return c.ProductsObj(obj)
}

func (c *Client) doCache(ehash string, ps []*structs.Product) {
	if len(ps) > 0 && c.cacheEnabled() {
		// 缓存
		for _, p := range ps {
			p.CachedAt = time.Now().Format("2006-01-02 15:04:05")
		}
		err := c.hashCache.SetWithExpire(ehash, ps, c.hashCacheMinute*60)
		if err != nil {
			log.Println("[WARNING] cache failed:", err)
		}

		if time.Since(c.lastWriteFileCacheTime) > c.writeFileCacheSecond {
			go c.writeCacheToFile()
		}
	}
}

/*
ProductsOfHashes 批量查询，只查询原始ehash，不进行更多的处理

hashes 每一个都是ehash
*/
func (c *Client) ProductsOfHashes(ehashes []string) (*cloudengine.HashResults, error) {
	var ps cloudengine.HashResults

	var restEHases []string
	// 先从缓存取
	if c.cacheEnabled() {
		for i := range ehashes {
			ehash := ehashes[i]
			if c.hashCache.Has(ehash) {
				if v, err := c.hashCache.Get(ehash); err == nil {
					products := v.([]*structs.Product)
					// 排除黑名单
					if len(products) == 1 && products[0].Name == cloudengine.BLOCKED {
						return nil, nil
					}
					ps.Add(ehash, products)
				} else {
					log.Println("[WARNING] cache failed:", err)
				}
			} else {
				restEHases = append(restEHases, ehash)
			}
		}
	}

	if len(restEHases) == 0 {
		return &ps, nil
	}

	// 再取云端
	sutraHash, err := c.cc.ProductsOfHashes(restEHases)
	if err != nil {
		return &ps, err
	}

	// 剩余
	restEHasesMap := make(map[string]int)
	for i := range restEHases {
		restEHasesMap[restEHases[i]]++
	}

	sutraHash.EachHash(func(ehash string, products []*structs.Product) bool {
		ps.Add(ehash, products)
		c.doCache(ehash, products)
		delete(restEHasesMap, ehash)
		return true
	})

	for ehash, _ := range restEHasesMap {
		c.doCache(ehash, []*structs.Product{
			&structs.Product{
				EHash: ehash,
				Name:  cloudengine.BLOCKED, // 云端没有但是查过的，也做为黑名单进行缓存
			},
		})
	}

	return &ps, nil
}

func (c *Client) writeCacheToFile() {
	if !c.cacheEnabled() {
		return
	}

	c.cacheMutex.Lock()
	defer c.cacheMutex.Unlock()

	r := c.hashCache.GetALL(true)
	var ps []*structs.Product
	for _, v := range r {
		ps = append(ps, v.([]*structs.Product)...)
	}
	d, err := json.Marshal(ps)
	if err != nil {
		log.Println("[WARNING] json marshal cache failed,", err)
	}
	ioutil.WriteFile(c.hashCacheFile, d, 0666)

	c.lastWriteFileCacheTime = time.Now()

}

// loadHashCacheFile 加载hash缓存文件
func (c *Client) loadHashCacheFile() error {
	b, err := ioutil.ReadFile(c.hashCacheFile)
	if err != nil {
		return err
	}

	var ps []*structs.Product
	if err = json.Unmarshal(b, &ps); err != nil {
		log.Println("[WARNING] load cache file failed", err)
	}

	psmap := make(map[string][]*structs.Product)
	for _, p := range ps {
		psmap[p.EHash] = append(psmap[p.EHash], p)
	}

	for k, v := range psmap {
		if err = c.hashCache.SetWithExpire(k, v, c.hashCacheMinute); err != nil {
			log.Println("[WARNING] set cache failed", err)
		}
	}

	return nil
}

/*
Wait 等待后台任务完成
*/
func (c *Client) Wait() {
	if !c.openCloudQuery {
		return
	}

	c.cc.Wait()
}

// IPProducts 查询公网IP的产品列表
func (c *Client) IPProducts(ip string) (res []string, err error) {
	if !c.openCloudQuery {
		return
	}

	return c.cc.IPProducts(ip)
}
