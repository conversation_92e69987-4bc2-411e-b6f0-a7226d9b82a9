<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Rules</title>
    <style>
          table
          {
              border-collapse: collapse;
              margin: 0 auto;
              text-align: center;
              width: 100%;
          }
        table td, table th
        {
            border: 1px solid #cad9ea;
            color: #666;
        }
        table thead th
        {
            background-color: #CCE8EB;
        }
        table tr:nth-child(odd)
        {
            background: #fff;
        }
        table tr:nth-child(even)
        {
            background: #F5FAFA;
        }

        table tr.ok {
            background-color: #9ED99D;
        }
    </style>
</head>
<body>
<p>
    All records:{{.AllSize}}  / Only subdomain records:{{.IsSubdomainSize}}
</p>
    <table>
        <tr><th>ID</th><th>Product</th><th>Rule</th><th>Fields</th><th>IsSubdomain</th><th>FOFASize</th><th>FidSize</th><th>OK</th><th>Reason</th></tr>
        {{range .Rules}}
            <tr {{if .OK}}class="ok"{{end}} {{if not .IsSubdomain}}hidden{{end}} >
                <td>{{.ID}}</td>
                <td>{{.Product}}</td>
                <td>{{.Content}}</td>
                <td>{{.Fields}}</td>
                <td>{{.IsSubdomain}}</td>
                <td>{{.FofaSize}}</td>
                <td>{{.HashNum}}</td>
                <td>{{.OK}}</td>
                <td>{{.Reason}}</td>
            </tr>
        {{end}}
    </table>
</body>
</html>