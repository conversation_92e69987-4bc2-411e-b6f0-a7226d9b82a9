package main

import (
	"bufio"
	"context"
	"flag"
	"fmt"
	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/rulengine"
	"github.com/valyala/fastjson"
	"log"
	"os"
	"time"
)

func check(c *gosutra.Client, j string) {
	s := time.Now()
	defer func() {
		log.Println("tag rules costs ", time.Since(s))
	}()

	log.Println("+++ check host:", fastjson.GetString([]byte(j), "host"), "timestamp", fastjson.GetString([]byte(j), "lastupdatetime"))
	defer log.Println("--- check host:", fastjson.GetString([]byte(j), "host"))
	if err := fastjson.Validate(j); err != nil {
		return
	}
	v, err := c.Products(j)
	if err != nil {
		log.Println(j)
		//panic(err)
		log.Println("[ERROR] product failed:", err)
		return
	}
	for _, product := range v {
		fmt.Println(product)
	}
}

func main() {
	var (
		server           string
		testFile         string
		ruleFile         string
		closeLocalEngine bool
		cloudQuery       bool
		queryRawJson     bool
		debug            bool
		openGzip         bool
		hashCacheMinute  int
	)
	flag.StringVar(&server, "server", "http://***********:2333", "")
	flag.StringVar(&testFile, "testFile", "", "for test one json file")
	flag.BoolVar(&closeLocalEngine, "closeLocalEngine", false, "")
	flag.BoolVar(&cloudQuery, "openCloud", false, "")
	flag.BoolVar(&queryRawJson, "queryRawJson", false, "")
	flag.BoolVar(&debug, "debug", false, "")
	flag.BoolVar(&openGzip, "openGzip", false, "")
	flag.StringVar(&ruleFile, "ruleFile", "", "ruleFile to load")
	flag.IntVar(&hashCacheMinute, "hashCacheMinute", 10, "set 0 to close cache")
	flag.Parse()

	gosutra.LoadInnerRules()

	c := gosutra.NewClient(
		gosutra.WithServer(server),
		gosutra.WithCloseLocalEngine(closeLocalEngine),
		gosutra.WithCloudQuery(cloudQuery),
		gosutra.WithQueryRawJson(queryRawJson),
		gosutra.WithDebug(debug),
		gosutra.WithOpenGzip(openGzip),
		gosutra.WithHashCacheMinute(hashCacheMinute),
		gosutra.WithRuleFile(context.Background(), ruleFile, false),
	)
	log.Println("load rules complete, size:", rulengine.RuleSize())

	if len(testFile) > 0 {
		fc, err := os.ReadFile(testFile)
		if err != nil {
			panic(err)
		}
		check(c, string(fc))
		time.Sleep(time.Second * 5)
		return
	}

	//a, _ := ioutil.ReadFile("a.txt")
	//check(c, string(a))
	//return

	scanner := bufio.NewScanner(os.Stdin)
	scanner.Buffer([]byte{}, bufio.MaxScanTokenSize*1024)
	for scanner.Scan() { // internally, it advances token based on sperator
		line := scanner.Text()
		if len(line) > 0 {
			check(c, line)
		}
	}

	if err := scanner.Err(); err != nil {
		fmt.Println(err)
	}

}
