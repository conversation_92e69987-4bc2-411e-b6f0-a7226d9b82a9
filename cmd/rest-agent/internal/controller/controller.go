package controller

import (
	stderr "errors"
	"fmt"
	"git.gobies.org/sutra/gosutra"
	"github.com/pkg/errors"
)

type (
	sutraConfig struct {
		Server           string // 服务器地址
		CloseLocalEngine bool   // 是否关闭本地殷勤
		OpenCloudQuery   bool   // 是否开启云引擎
		OpenQueryJson    bool   // 是否开启查询原始json模式
		OpenGzip         bool   // 大文件启用gzip
		OpenDebug        bool   // 开启调试
		HashCacheMinute  int    // 云端缓存的时间
		HashCacheFile    string // hash本地缓存文件
	}

	ConfigScan interface {
		// Scan data scan into any
		Scan(pointer interface{}, mapping ...map[string]string) error
	}
)

var (
	sutraConf *sutraConfig

	//ErrRestError http 通用服务错误
	ErrRestError = stderr.New("rest service error")
)

// Init the config of sutra from ConfigScan
func Init(data ConfigScan) {
	fmt.Printf("init sutra config %v from config (%v)\n", sutraConf, data)
	if err := data.Scan(&sutraConf); err != nil {
		panic(errors.WithMessage(err, "can not scan config of sutra"))
	}
	opts := make([]gosutra.ClientOption, 0)
	opts = append(opts, gosutra.WithServer(sutraConf.Server))
	opts = append(opts, gosutra.WithCloseLocalEngine(sutraConf.CloseLocalEngine))
	opts = append(opts, gosutra.WithCloudQuery(sutraConf.OpenCloudQuery))
	opts = append(opts, gosutra.WithQueryRawJson(sutraConf.OpenQueryJson))
	opts = append(opts, gosutra.WithOpenGzip(sutraConf.OpenGzip))
	opts = append(opts, gosutra.WithDebug(sutraConf.OpenDebug))
	opts = append(opts, gosutra.WithHashCacheMinute(sutraConf.HashCacheMinute))
	opts = append(opts, gosutra.WithHashCacheFile(sutraConf.HashCacheFile))
	client = gosutra.NewClient(opts...)
}
