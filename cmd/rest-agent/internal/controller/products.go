package controller

import (
	"context"
	"encoding/json"
	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/cmd/rest-agent/logger"
	"git.gobies.org/sutra/gosutra/structs"
	"github.com/pkg/errors"
	"time"

	"git.gobies.org/sutra/gosutra/cmd/rest-agent/api/v1"
)

var (
	Product = cProduct{}    //product controller instance
	client  *gosutra.Client // sutra client instance
)

type (
	cProduct struct{}
)

// Products controller of product
func (c *cProduct) Products(_ context.Context, req *v1.ProductsReq) (res *v1.ProductsRes, err error) {
	logger.GetLogger().Debugf("hello=> req %v", req)
	var buf []byte
	var prods []*structs.Product

	buf, err = json.Marshal(req)
	if err != nil {
		logger.GetLogger().Errorf("can not unmarshal of req %v, err=%v", req, err)
		return nil, errors.WithMessage(ErrRestError, err.Error())
	}

	prods, err = client.Products(string(buf))
	if err != nil {
		logger.GetLogger().Warnf("can not find product for %v , err=%v", req, err)
		return
	}

	r := make([]*v1.ProductDetail, 0, len(prods))
	for _, prod := range prods {
		r = append(r, &v1.ProductDetail{
			Name:           prod.Name,
			Rule:           prod.Rule,
			RuleId:         prod.RuleId,
			Level:          prod.Level,
			Category:       prod.Category,
			ParentCategory: prod.ParentCategory,
			SoftHard:       prod.SoftHard,
			Company:        prod.Company,
			Version:        prod.Version,
			From:           prod.From,
			CachedAt: func() int {
				// todo use time CST instead of time.Local to avoid env config config timezone error
				t, err := time.ParseInLocation("2006-01-02 15:04:05", prod.CachedAt, time.Local)
				if err != nil {
					logger.GetLogger().Warnf("prod cache at time format unexpect,req=%v, %v", req, err)
				}
				return int(t.Unix())
			}(), // todo create new function,
			Ehash: prod.EHash,
		})
	}

	res = &v1.ProductsRes{Products: r}
	return
}
