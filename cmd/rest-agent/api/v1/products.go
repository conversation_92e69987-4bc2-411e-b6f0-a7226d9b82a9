package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type ProductsReq struct {
	g.Meta `path:"/api/v1/products" tags:"Products" method:"post" summary:"You first hello api"`

	Body   string `json:"body,omitempty"`
	Header string `json:"header,omitempty"`
	Banner string `json:"banner,omitempty"`
	Cert   string `json:"cert,omitempty"`
}

type ProductsRes struct {
	g.Meta   `mime:"application/json" example:"{}"`
	Products []*ProductDetail `json:"data,omitempty"`
}

type ProductDetail struct {
	Name           string `json:"name,omitempty" `
	Rule           string `json:"rule,omitempty"`
	RuleId         string `json:"rule_id,omitempty"`
	Level          string `json:"level,omitempty"`
	Category       string `json:"category,omitempty"`
	ParentCategory string `json:"parent_category,omitempty"`
	SoftHard       string `json:"soft_hard,omitempty"`
	Company        string `json:"company,omitempty"`
	Version        string `json:"version,omitempty"`
	From           string `json:"from,omitempty"`
	CachedAt       int    `son:"cached_at,omitempty"`
	Ehash          string `json:"ehash,omitempty"`
}
