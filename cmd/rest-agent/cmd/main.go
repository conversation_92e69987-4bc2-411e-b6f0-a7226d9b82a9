package main

import (
	"git.gobies.org/sutra/gosutra/cmd/rest-agent/internal/cmd"
	"git.gobies.org/sutra/gosutra/cmd/rest-agent/logger"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/spf13/cobra"
	"strconv"
	"time"
)

var (
	Version = "1.0.0"
)

func main() {
	app := getRootCmd()
	err := app.Execute()
	if err != nil {
		logger.GetLogger().Errorf("execute faild. %v", err)
	}
}

func getRootCmd() *cobra.Command {
	rootCmd := &cobra.Command{
		Use:   "sutra",
		Short: "sutra is the engine of ip assets recognize tool.",
		Long: " sutra" +
			"(c) " + strconv.Itoa(time.Now().Year()) + " ",
		Version: Version,
		Run: func(_ *cobra.Command, args []string) { //todo 修改命令行结构
			logger.Init()
			cmd.Init()
			cmd.Main.Run(gctx.New())
		},
	}

	return rootCmd
}
