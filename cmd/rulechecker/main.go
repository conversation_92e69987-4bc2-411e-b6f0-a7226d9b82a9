/*
rulechecker 检查满足sutra的规则
输入的是json，里面至少有一个指明rule的字段，进行有效性判断，并且确定fofa上有数据
sutra与fofa规则的本质不同在于：一）云端存储归一化的结果，本地只检查无法归一化的规则；二）归一化的是所有页面，而不是一个系统；三）没有分词的影响
*/
package main

import (
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"git.gobies.org/sutra/gosutra/fofa"
	"git.gobies.org/sutra/gosutra/internal/pipeinit"
	"git.gobies.org/sutra/gosutra/rulengine"
	"git.gobies.org/sutra/gosutra/structs"
	"log"
	"time"
)

var (
	ruleField = flag.String("ruleField", "rule", "json field contains rule")
	idField   = flag.String("idField", "product", "json id field as key")
	fofaUrl   = flag.String("fofaUrl", fofa.GenFofaUrlFromEnvi(), "<url>/?email=<email>&key=<key>&version=<v2>&tlsdisabled=<false>&debuglevel=<0>&full=<true>")
)

func isValidJSON(jsonStr string) (map[string]interface{}, error) {
	var m map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &m); err != nil {
		return nil, err
	}
	if _, ok := m[*ruleField]; !ok {
		return nil, errors.New(fmt.Sprintf("ruleField of %s not exsits", *ruleField))
	}
	if _, ok := m[*idField]; !ok {
		return nil, errors.New(fmt.Sprintf("idField of %s not exsits", *idField))
	}

	return m, nil
}

func main() {
	flag.Parse()

	fofacli := fofa.NewFofaCli(*fofaUrl)

	pipeinit.ProcessFileOrInput(flag.Args(), func(line string) bool {
		var m map[string]interface{}
		var err error
		var r *rulengine.RuleT

		// 检查json和字段
		m, err = isValidJSON(line)
		if err != nil {
			panic(err)
		}

		// 规则合法性检查
		rule := m[*ruleField].(string)
		id := m[*idField].(string)
		r, err = rulengine.NewRule(id, rule)
		if err != nil {
			log.Fatalf("parse rule failed: %s", err)
			return false
		}

		log.Printf("checking %s [%s]...", id, rule)

		// 字段有效性检查
		for _, field := range r.Fields {
			if field != "server" && field != "header" && field != "body" {
				log.Fatalf("not allowed field: %s", field)
			}
		}

		// fofa内容检查
		size := 0
		fofacli.Fetch(rule, "host,server,header,body,ip,port", 1, func(fields []string, allSize int32) bool {
			size = int(allSize)

			s := time.Now()
			httpInfo := map[string]interface{}{
				"timestamp": s.String(),
				"host":      fields[0],
				"server":    fields[1],
				"header":    fields[2],
				"body":      fields[3],
				"ip":        fields[4],
				"port":      fields[5],
			}
			d, err := json.Marshal(httpInfo)
			if err != nil {
				log.Fatalf("json failed: %s", err)
				return false
			}

			obj, err := structs.NewJsonObj(string(d))
			if err != nil {
				log.Fatalf("NewJsonObj failed: %s", err)
				return false
			}

			if !r.Check(obj) {
				log.Fatalf("rule check failed: %s", rule)
				return false
			}

			return true
		})

		if size == 0 {
			log.Fatalf("size is 0")
			return false
		}

		log.Printf("ok, size:%d\n", size)

		return true
	})
}
