package controller

import (
	v1 "client-api/api/v1"
	"client-api/internal/service"
	"context"
	"encoding/json"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/util/gconv"
)

var (
	Rule = cRule{}
)

type cRule struct{}

func (c *cRule) Match(ctx context.Context, req *v1.RuleMatchReq) (res *v1.RuleMatchRes, err error) {
	g.Log().Infof(ctx, "req.MatchData: %+v", req.MatchData)
	g.Log().Infof(ctx, "req.MatchData.Banner: %q", req.MatchData.Banner)
	g.Log().Infof(ctx, "req.MatchData.Protocol: %q", req.MatchData.Protocol)

	// 修复：直接序列化 MatchData，不使用 gjson
	dataBytes, err := json.Marshal(req.MatchData)
	if err != nil {
		g.Log().<PERSON><PERSON><PERSON>(ctx, "marshal MatchData error: %v", err)
		return
	}
	data := string(dataBytes)
	g.Log().Infof(ctx, "req data %v", data)
	ps, err := service.Rule().Match(ctx, data)
	if err != nil {
		g.Log().Errorf(ctx, "req Match rule error %v", err)
		return
	}

	if ps != nil {
		r := make(v1.RuleMatchRes, 0)
		err = gconv.Structs(ps, &r)
		res = &r
	}
	return
}

func formatRuleLine(ps []*v1.Product) string {
	rules := ""
	for _, r1 := range ps {
		s1, err := gjson.New(r1).ToJsonString()
		if err != nil {
			return ""
		}
		rules += s1 + "\n"
	}
	return rules
}

func (c *cRule) Add(ctx context.Context, req *v1.RuleAddReq) (res v1.RuleAddRes, err error) {
	rules := formatRuleLine(req.Rules)
	glog.Info(ctx, "rules:", rules)
	r := service.Rule().Add(ctx, rules)
	res = v1.RuleAddRes(r)
	return
}

func (c *cRule) Reset(ctx context.Context, req *v1.RuleResetReq) (res v1.RuleResetRes, err error) {
	rules := formatRuleLine(req.Rules)
	glog.Info(ctx, "rules:", rules)

	r := service.Rule().Reset(ctx, rules)
	res = v1.RuleResetRes(r)
	return
}
