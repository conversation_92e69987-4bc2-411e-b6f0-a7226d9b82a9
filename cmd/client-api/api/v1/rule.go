package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type Product struct {
	Name           string `json:"product" dc:"产品名"`
	Rule           string `json:"rule,omitempty"`
	RuleId         string `json:"rule_id,omitempty"`
	Level          string `json:"level"`
	Category       string `json:"category" dc:"产品分类"`
	ParentCategory string `json:"parent_category" dc:"二级分类"`
	SoftHard       string `json:"softhard" dc:"软硬件层"`
	Company        string `json:"company" dc:"公司"`
	Version        string `json:"version" dc:"版本号"`
	From           string `json:"from" dc:"来源 local, user"` //来源：local、user、cloud
	CachedAt       string `json:"cached_at,omitempty"`
	EHash          string `json:"ehash,omitempty"`
}

type MatchData struct {
	Body     string `json:"body,omitempty" dc:"http body"`
	SubBody  string `json:"sub_body,omitempty" dc:"http body"`
	Cert     string `json:"cert,omitempty" dc:"http cert"`
	Title    string `json:"title,omitempty" dc:"http title"`
	Domain   string `json:"domain,omitempty" dc:"域名"`
	Header   string `json:"header,omitempty" dc:"http header"`
	IconHash string `json:"icon_hash,omitempty" dc:"图标hash值"`
	Banner   string `json:"banner,omitempty" dc:"协议banner"`
	Protocol string `json:"protocol,omitempty" dc:"协议名"`
	Server   string `json:"server,omitempty" dc:"server头信息"`
}

type RuleMatchReq struct {
	g.Meta `path:"/product/match" tags:"Rule" method:"post" summary:"match product"`
	MatchData
}

type RuleMatchRes []Product

type RuleAddReq struct {
	g.Meta `path:"/rule/add" tags:"Rule" method:"post" summary:"add rule"`
	Rules  []*Product `json:"rules" p:"rules" dc:"规则信息，json字符串"`
}

type RuleAddRes int

type RuleResetReq struct {
	g.Meta `path:"/rule/reset" tags:"Rule" method:"post" summary:"reset rule"`
	Rules  []*Product `json:"rules" p:"rules" dc:"规则信息，json字符串, 重置的新规则,可以为空"`
}

type RuleResetRes int
