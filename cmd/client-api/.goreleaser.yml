project_name: sutra-client
env:
  - GO111MODULE=on
  - GOPROXY=https://goproxy.io,direct
  - CGO_ENABLED=0

before:
  hooks:
    # - go get -u github.com/golangci/golangci-lint/cmd/golangci-lint
    # you may remove this if you don't use vgo

builds:
  - goos:
      - darwin
      - linux
      - windows
    goarch:
      - 386
      - amd64
      - arm64
    flags:
      - -trimpath
    ldflags:
      - -s -w
    asmflags:
      - all=-trimpath={{.Env.GOPATH}}
    gcflags:
      - all=-trimpath={{.Env.GOPATH}}

gitlab_urls:
  api: https://git.baimaohui.net/api/v4/
  download: https://git.baimaohui.net
  # set to true if you use a self-signed certificate
  # skip_tls_verify: true

archives:
  - name_template: "{{ .ProjectName }}_{{ .Os }}_{{ .Arch }}"
    files:
      - none*

changelog:
  skip: true
        
release:
  # Repo in which the release will be created.
  # Default is extracted from the origin remote URL or empty if its private hosted.
  # set GITHUB_TOKEN first
  gitlab:
    owner: sutra
    name: client-api-binary

  # If set to true, will not auto-publish the release.
  # Default is false.
  # draft: true

  # If set to auto, will mark the release as not ready for production
  # in case there is an indicator for this in the tag e.g. v1.0.0-rc1
  # If set to true, will mark the release as not ready for production.
  # Default is false.
  prerelease: auto

  # Header template for the release body.
  # Defaults to empty.
  header: |
    ## Some highlights:

    ## Features
    - 修复传递json，banner为空字符串无法匹配的问题

  # You can disable this pipe in order to not upload any artifacts.
  # Defaults to false.
  # disable: true
