/*
randomjson 从fofa中随机获取一条数据生成json
	../randomjson/randomjson | ../client/client | jq

参数：
./randomjson -size 0 -sleepSecond 0

*/
package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"git.gobies.org/sutra/gosutra/algo"
	"git.gobies.org/sutra/gosutra/fofa"
	"git.gobies.org/sutra/gosutra/utils"
	"time"
)

var (
	fofaUrl     string
	filter      string //
	sleepSecond int    // sleepSecond
	size        int
)

type DataJson struct {
	Host           string `json:"host"`
	IP             string `json:"ip"`
	Port           string `json:"port"`
	Header         string `json:"header,omitempty"`
	Body           string `json:"body,omitempty"`
	Cert           string `json:"cert,omitempty"`
	Banner         string `json:"banner,omitempty"`
	NHash          string `json:"nhash,omitempty"`
	EHash          string `json:"ehash,omitempty"`
	FoHash         string `json:"fohash,omitempty"`
	LastUpdateTime string `json:"lastupdatetime,omitempty"`
	Server         string `json:"server,omitempty"`
}

func main() {
	flag.StringVar(&filter, "filter", "type=subdomain", "fofa raw query")
	flag.StringVar(&fofaUrl, "fofaUrl", fofa.GenFofaUrlFromEnvi(), "<url>/?email=<email>&key=<key>&version=<v2>&tlsdisabled=<false>&debuglevel=0&full=true")
	flag.IntVar(&size, "size", 1, "0 means no stop")
	flag.IntVar(&sleepSecond, "sleepSecond", 1, "stype: service or subdomain or either")
	flag.Parse()

	fields := "host,ip,port,header,body,cert,banner,lastupdatetime,server"

	count := 0
	fofacli := fofa.NewFofaCli(fofaUrl)

	for {
		r := fofacli.FetchRaondomOne(filter, fields)
		if len(r) > 0 {
			d := DataJson{
				Host:           r[0],
				IP:             r[1],
				Port:           r[2],
				Header:         r[3],
				Body:           r[4],
				Cert:           r[5],
				Banner:         r[6],
				LastUpdateTime: r[7],
				Server:         r[8],
			}
			d.NHash = utils.BodyNormalizeHash(d.Body)
			d.EHash = algo.SignString(d.NHash)
			d.FoHash = algo.EncryptStringToBase64(d.NHash, "fofafofa")

			v, _ := json.Marshal(d)
			fmt.Println(string(v))
		}
		count++

		if size > 0 && count >= size {
			break
		}
		if sleepSecond > 0 {
			time.Sleep(time.Second * time.Duration(sleepSecond))
		}

	}

}
