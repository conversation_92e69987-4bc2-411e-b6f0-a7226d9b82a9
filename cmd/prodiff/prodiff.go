/*
用于对比效果，sutra与wappalyzer
*/
package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"git.gobies.org/sutra/gosutra"
	"git.gobies.org/sutra/gosutra/fofa"
	"git.gobies.org/sutra/gosutra/internal/genmap"
	"git.gobies.org/sutra/gosutra/internal/pipeinit"
	wappalyzer "github.com/projectdiscovery/wappalyzergo"
	"log"
	"time"
)

var (
	fofaUrl = flag.String("fofaUrl", fofa.GenFofaUrlFromEnvi(), "<url>/?email=<email>&key=<key>&version=<v2>&tlsdisabled=<false>&debuglevel=<0>&full=<true>")
)

func main() {
	flag.Parse()

	gosutra.LoadInnerRules()
	sutraCli := gosutra.NewClient(gosutra.WithCloudQuery(true),
		gosutra.WithQueryRawJson(false),
		gosutra.WithServer("http://api.sutrad.org/"),
		gosutra.WithHashCacheMinute(10))
	wappalyzerClient, err := wappalyzer.New()
	if err != nil {
		panic(err)
	}

	pipeinit.ProcessFileOrInput(flag.Args(), func(line string) bool {
		var httpInfo map[string]interface{}
		if err := json.Unmarshal([]byte(line), &httpInfo); err != nil {
			log.Println("[ERROR]", err)
			return false
		}
		s := time.Now()
		if v := genmap.GenProductsInfoFromMap(httpInfo, sutraCli, wappalyzerClient, s); v != nil {
			fmt.Println(string(v))
		}
		return true
	})
}
