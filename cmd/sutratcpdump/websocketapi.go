package main

import (
	"git.gobies.org/sutra/gosutra/cmd/sutratcpdump/resources"
	"github.com/gorilla/websocket"
	"log"
	"net/http"
	"strings"
	"sync"
)

func serveHome(w http.ResponseWriter, r *http.Request) {
	//homeTemplate.Execute(w, "ws://"+r.Host+"/ws")
	html, _ := resources.WebFs.ReadFile("index.html")
	w.Write([]byte(strings.ReplaceAll(string(html), "{{{WS}}}", "ws://"+r.Host+"/ws")))
}

var upgrader = websocket.Upgrader{}
var liveWebsockets sync.Map

type liveMessage struct {
	Type string                 `json:"type"`
	Data map[string]interface{} `json:"data"`
}

func (lm *liveMessage) MsgType() string {
	return lm.Type
}

func (lm *liveMessage) MsgData() map[string]interface{} {
	return lm.Data
}

type wsConnWithLock struct {
	sync.Mutex
	*websocket.Conn
}

// 广播消息
func broadCast(msg []byte) {
	liveWebsockets.Range(func(k, v interface{}) bool {
		go func(ws *wsConnWithLock) {
			ws.Lock()
			defer ws.Unlock()
			if writer, err := ws.NextWriter(websocket.TextMessage); err != nil {
				liveWebsockets.Delete(k)
				return
			} else {
				writer.Write(msg)
				writer.Close()
			}
		}(v.(*wsConnWithLock))
		return true
	})
}

func serveWs(w http.ResponseWriter, r *http.Request) {
	ws, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Println("upgrade:", err)
		return
	}

	defer func() {
		liveWebsockets.Delete(r.RemoteAddr)
		ws.Close()
	}()

	liveWebsockets.Store(r.RemoteAddr, &wsConnWithLock{Conn: ws})
	for {
		var msg liveMessage
		err := ws.ReadJSON(&msg)
		if err != nil {
			if !websocket.IsCloseError(err) &&
				!websocket.IsUnexpectedCloseError(err) {
				log.Println("websocket read failed", err)
			}
			break
		}

		log.Println(msg)
	}

}

func startWebsocket() {
	if len(*websocketBind) > 0 {
		http.Handle("/static/", http.StripPrefix("/static/",
			http.FileServer(http.FS(resources.WebFs))))
		http.HandleFunc("/", serveHome)
		http.HandleFunc("/ws", serveWs)
		log.Fatal(http.ListenAndServe(*websocketBind, nil))
	}
}
