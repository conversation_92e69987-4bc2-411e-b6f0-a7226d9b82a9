* {
  padding: 0;
  margin: 0;
}

a {
  cursor: pointer;
}

.gb-com-td>div {
  padding: 0 10px;
  text-align: center;
}

.gb-com-td img {
  width: 11px;
  height: 11px;
  margin-right: 5px;
}

.gb-com-td>div.empty {
  padding: 0;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  color: #85a1ce;
  text-align: center;
}

.gb-com-td>div.empty:nth-child(1) {
  background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div.empty:nth-child(2) {
  background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div.empty:nth-child(3) {
  background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div.empty:nth-child(4) {
  background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div.empty:nth-child(5) {
  background-color: rgba(192, 210, 237, 0.2);
}

.gb-com-td>div:nth-child(1) {
  background-color: rgba(187, 231, 250, 0.32);
  border: 0;
}

.gb-com-td>div:nth-child(2) {
  background-color: rgba(144, 213, 243, 0.4);
  border: 0;
  margin-top: 1px;
}

.gb-com-td>div:nth-child(3) {
  background-color: rgba(116, 202, 239, 0.6);
  border: 0;
  margin-top: 1px;
}

.gb-com-td>div:nth-child(4) {
  background-color: rgba(91, 191, 234, 0.8);
  border: 0;
  margin-top: 1px;
}

.gb-com-td>div:nth-child(5) {
  background-color: rgba(63, 176, 224, 1);
  border: 0;
  margin-top: 1px;
}

.gb-com-td>div span {
  display: inline-block;
  font-size: 12px;
  color: #1098d2 !important;
  padding: 0 6px;
  height: 16px;
  margin: 3px 5px 3px 0;
  line-height: 16px;
  border-radius: 7px;
  background-color: rgba(255, 255, 255, 0.75);
}

.operate,
.config-sec {
  text-align: right;
  margin: 20px;
}

/* cost颜色区分 */
.cost-red {
  color: red;
}

.cost-green {
  color: green;
}