package main

import (
	"bufio"
	"bytes"
	"context"
	"encoding/hex"
	"fmt"
	"git.gobies.org/sutra/gosutra/cmd/sutratcpdump/protocols"
	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"github.com/google/gopacket/reassembly"
	"io"
	"log"
	"math"
	"reflect"
	"sync"
)

const (
	usePool = false
)

func newBuffer() *bytes.Buffer {
	return bytes.NewBuffer(make([]byte, 0, 65535))
}

var pool = sync.Pool{
	// New creates an object when the pool has nothing available to return.
	// New must return an interface{} to make it flexible. You have to cast
	// your type after getting it.
	New: func() interface{} {
		// Pools often contain things like *bytes.Buffer, which are
		// temporary and re-usable.
		return newBuffer()
	},
}

// 状态机， 0 等待进入处理，1 接受正在处理，2 拒绝处理，3 结束处理
type state int

const (
	StateNone    state = 0
	StateWorking       = 1
	StateEnd           = 3
)

func (s *state) IsNone() bool {
	return *s == StateNone
}
func (s *state) SetWorking() {
	*s = StateWorking
}
func (s *state) IsWorking() bool {
	return *s == StateWorking
}
func (s *state) SetEnd() {
	*s = StateEnd
}
func (s *state) IsEnd() bool {
	return *s == StateEnd
}

/*
 * TCP stream
 */

/* It's a connection (bidirectional) */
type tcpStream struct {
	ctx            context.Context
	cancel         context.CancelFunc
	factory        *tcpStreamFactory
	tcpstate       *reassembly.TCPSimpleFSM
	fsmerr         bool // 用于统计fsm的错误数，根据conn去重
	net, transport gopacket.Flow
	srcip          string
	dstip          string
	srcport        string
	dstport        string
	urls           []string
	proto          protocols.Protocol // 解析的协议引擎
	//reject         bool               //是否当前会话不要了
	state state // 状态机， 0 等待进入处理，1 接受正在处理，2 拒绝处理，3 结束处理
	wg    sync.WaitGroup

	//reqBufCh  chan []byte // 客户端到服务端的数据量channel
	//respBufCh chan []byte // 服务端到客户端的数据量channel
	//once      sync.Once

	reqBuf  *bytes.Buffer
	respBuf *bytes.Buffer

	sync.Mutex
}

func (t *tcpStream) clientIdent() string {
	return t.net.String() + ":" + t.transport.String()
}
func (t *tcpStream) serverIdent() string {
	return t.net.Reverse().String() + ":" + t.transport.Reverse().String()
}

// isExpectErr 判断是否预期的错误
func isExpectErr(err error) bool {
	if err != io.EOF && err != io.ErrUnexpectedEOF &&
		err.Error() != "http: unexpected EOF reading trailer" && err != protocols.ErrNotImpl {
		return false
	}
	return true
}

func (t *tcpStream) checkData(buf *bytes.Buffer,
	processFn func(ctx context.Context, reader *bufio.Reader) (interface{}, error),
	onDataFn func(name string, data interface{}, t *tcpStream), d []byte) bool {

	t.Lock()
	defer t.Unlock()
	buf.Write(d)

	tmpReader := bytes.NewReader(buf.Bytes())
	r := bufio.NewReader(tmpReader)
	data, err := processFn(t.ctx, r)
	if err == nil {
		onDataFn(t.proto.Info()["protocol"].(string), data, t)

		buf.Reset()
		if tmpReader.Len() > 0 {
			io.Copy(buf, tmpReader)
		}
	} else if !isExpectErr(err) {
		// var errTrailerEOF = errors.New("http: unexpected EOF reading trailer")
		t.state.SetEnd() // 报错就结束
		return false
	}

	return true
}

//func (t *tcpStream) run() {
//	defer func() {
//		t.state.SetEnd() // 让写入方close
//		if *debug {
//			log.Println("run out", t.clientIdent())
//		}
//
//		t.wg.Done()
//		t.factory.wg.Done()
//	}()
//
//	var d []byte
//	var ok bool
//	var err error
//	var data interface{}
//
//	var reqBuf *bytes.Buffer
//	var respBuf *bytes.Buffer
//	if usePool {
//		reqBuf = pool.Get().(*bytes.Buffer)
//		respBuf = pool.Get().(*bytes.Buffer)
//		defer func() {
//			reqBuf.Reset()
//			pool.Put(reqBuf)
//			respBuf.Reset()
//			pool.Put(respBuf)
//		}()
//	} else {
//		reqBuf = newBuffer()
//		respBuf = newBuffer()
//	}
//
//	checkData := func(buf *bytes.Buffer,
//		processFn func(ctx context.Context, reader *bufio.Reader) (interface{}, error),
//		onDataFn func(name string, data interface{}, t *tcpStream)) bool {
//
//		buf.Write(d)
//
//		tmpReader := bytes.NewReader(buf.Bytes())
//		r := bufio.NewReader(tmpReader)
//		data, err = processFn(t.ctx, r)
//		if err == nil {
//			onDataFn(t.proto.Info()["protocol"].(string), data, t)
//
//			buf.Reset()
//			if tmpReader.Len() > 0 {
//				io.Copy(buf, tmpReader)
//			}
//		} else if !isExpectErr(err) {
//			// var errTrailerEOF = errors.New("http: unexpected EOF reading trailer")
//			return false
//		}
//
//		return true
//	}
//
//	for {
//		switch t.state {
//		case StateNone:
//			time.Sleep(time.Millisecond * 100)
//			continue
//		}
//
//		select {
//		case <-t.ctx.Done():
//			return
//		case d, ok = <-t.reqBufCh:
//			if ok {
//				if *debug {
//					log.Println("fetch request data", t.clientIdent)
//				}
//				if !checkData(reqBuf, t.proto.DoClient, t.factory.OnClientData) {
//					return
//				}
//				continue
//			}
//		case d, ok = <-t.respBufCh:
//			if ok {
//				if *debug {
//					log.Println("fetch response data", t.serverIdent)
//				}
//				if !checkData(respBuf, t.proto.DoServer, t.factory.OnServerData) {
//					return
//				}
//			} else {
//				// 结束
//				return
//			}
//		case <-time.After(2 * time.Second):
//			// 解决只有request导致的死锁问题
//			// 超时说明没有数据读入了，要退出
//			if t.state.IsEnd() {
//				return
//			}
//		}
//	}
//}

func (t *tcpStream) Accept(tcp *layers.TCP, ci gopacket.CaptureInfo, dir reassembly.TCPFlowDirection, nextSeq reassembly.Sequence, start *bool, ac reassembly.AssemblerContext) bool {
	if t.state.IsEnd() {
		stats.reject++
		return false
	}

	// FSM
	if !t.tcpstate.CheckState(tcp, dir) {
		//Error("FSM", "%s: Packet rejected by FSM (state:%s)\n", t.ident, t.tcpstate.String())
		stats.rejectFsm++
		if !t.fsmerr {
			t.fsmerr = true
			stats.rejectConnFsm++
		}
		if !*ignorefsmerr {
			return false
		}
	}

	// Options
	//err := t.optchecker.Accept(tcp, ci, dir, nextSeq, start)
	//if err != nil {
	//	// 重复的包，丢弃 drop
	//	// 调试发现此包为以前序号的包，并且出现过。
	//	// mss BUG,server mss通过路由拆解成mss要求的包尺寸，
	//	// 因此不能判断包大小大于mss为错包
	//	if strings.Contains(fmt.Sprintf("%s", err), " > mss ") {
	//		//  > mss 包 不丢弃
	//	} else {
	//		//Error("OptionChecker", "%v ->%v : Packet rejected by OptionChecker: %s\n",  t.net, t.transport, err)
	//		stats.rejectOpt++
	//		if !*nooptcheck {
	//			return false
	//		}
	//	}
	//}

	// Checksum
	accept := true
	if *checksum {
		c, err := tcp.ComputeChecksum()
		if err != nil {
			//Error("ChecksumCompute", "%s: Got error computing checksum: %s\n", t.ident, err)
			accept = false
		} else if c != 0x0 {
			//Error("Checksum", "%s: Invalid checksum: 0x%x\n", t.ident, c)
			accept = false
		}
	}

	if t.state.IsNone() {
		if len(tcp.Payload) > 0 {
			l := int(math.Min(float64(len(tcp.Payload)), 10))
			if p, closed := protocols.Accept(dir == reassembly.TCPDirServerToClient, t.clientIdent(), tcp.Payload); p == nil {
				accept = false
				t.state.SetEnd()
				//t.cancel()
				log.Println("reject packet cause unknown protocol,", t.clientIdent(), hex.Dump(tcp.Payload[:l]))
			} else {
				//if *debug {
				//	log.Println("accept packet:", t.clientIdent(), hex.Dump(tcp.Payload[:l]))
				//}

				if closed {
					t.proto = p
					t.factory.OnProtocol(t)
					t.state.SetEnd()
				} else {
					*start = true
					t.state.SetWorking()
					t.proto = p
					//t.factory.wg.Add(1)
					//t.wg.Add(1)
					//go t.run()

					t.reqBuf = newBuffer()
					t.respBuf = newBuffer()
				}
			}
		}
	}

	if !accept {
		stats.reject++
	}

	//if *debug {
	//	log.Println("packet ", accept, t.clientIdent(), len(tcp.Payload))
	//}

	return accept
}

func (t *tcpStream) onData(dir reassembly.TCPFlowDirection, end bool, data []byte) {
	if *debug {
		id := t.serverIdent
		if dir == reassembly.TCPDirClientToServer {
			id = t.clientIdent
		}
		//log.Println("recv:", id, string(data))
		log.Println("onData in", reflect.TypeOf(id), dir, end, len(data))
		defer log.Println("onData out", reflect.TypeOf(id), dir, end, len(data))
	}

	if dir == reassembly.TCPDirClientToServer {
		//t.reqBufCh <- data
		go t.checkData(t.reqBuf, t.proto.DoClient, t.factory.OnClientData, data)
	} else {
		//t.respBufCh <- data
		go t.checkData(t.reqBuf, t.proto.DoServer, t.factory.OnServerData, data)
	}

	//if t.state.IsEnd() {
	//	go t.done()
	//	return
	//}
}

func (t *tcpStream) ReassembledSG(sg reassembly.ScatterGather, ac reassembly.AssemblerContext) {
	dir, _, end, skip := sg.Info()
	length, saved := sg.Lengths()

	// update stats
	sgStats := sg.Stats()
	if skip > 0 {
		stats.missedBytes += skip
	}
	stats.sz += length - saved
	stats.pkt += sgStats.Packets
	if sgStats.Chunks > 1 {
		stats.reassembled++
	}
	stats.outOfOrderPackets += sgStats.QueuedPackets
	stats.outOfOrderBytes += sgStats.QueuedBytes
	if length > stats.biggestChunkBytes {
		stats.biggestChunkBytes = length
	}
	if sgStats.Packets > stats.biggestChunkPackets {
		stats.biggestChunkPackets = sgStats.Packets
	}
	if sgStats.OverlapBytes != 0 && sgStats.OverlapPackets == 0 {
		fmt.Printf("Invalid overlap, bytes:%d, pkts:%d\n", sgStats.OverlapBytes, sgStats.OverlapPackets)
		//panic("Invalid overlap")
		return
	}
	stats.overlapBytes += sgStats.OverlapBytes
	stats.overlapPackets += sgStats.OverlapPackets

	//var ident string
	//if dir == reassembly.TCPDirClientToServer {
	//	ident = fmt.Sprintf("%v %v(%s): ", t.net, t.transport, dir)
	//} else {
	//	ident = fmt.Sprintf("%v %v(%s): ", t.net.Reverse(), t.transport.Reverse(), dir)
	//}
	//Debug("%s: SG reassembled packet with %d bytes (start:%v,end:%v,skip:%d,saved:%d,nb:%d,%d,overlap:%d,%d)\n", ident, length, start, end, skip, saved, sgStats.Packets, sgStats.Chunks, sgStats.OverlapBytes, sgStats.OverlapPackets)
	if skip == -1 && *allowmissinginit {
		// this is allowed
	} else if skip != 0 {
		// Missing bytes in stream: do not even try to parse it
		return
	}

	data := sg.Fetch(length)

	if length > 0 {
		if t.state.IsWorking() {
			if *hexdump {
				log.Println(hex.Dump(data))
				//Debug("Feeding http with:\n%s", hex.Dump(data))
			}

			t.onData(dir, end, data)
		} else {
			//log.Println("not ready:", t.clientIdent(), string(data))
		}
	}

}

//func (t *tcpStream) done() {
//	t.once.Do(func() {
//		close(t.reqBufCh)
//		close(t.respBufCh)
//		t.wg.Wait() //等待处理完毕
//		if *debug {
//			log.Println("done called", t.clientIdent)
//		}
//	})
//}

func (t *tcpStream) ReassemblyComplete(ac reassembly.AssemblerContext) bool {
	if *debug {
		log.Printf("%s: Connection closed\n", reflect.TypeOf(t.clientIdent))
	}

	//go t.done()
	t.state.SetEnd()

	// do not remove the connection to allow last ACK
	return true
}
