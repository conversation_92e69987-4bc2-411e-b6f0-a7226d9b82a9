### 运行
```shell script
./sutratcpdump -i en0
```

结合jq使用：
```shell script
./sutratcpdump -i en0 | jq
{
  "client_ip": "************",
  "client_port": "54409",
  "cost": "130.855µs",
  "ehash": "e76d3debeb6565a8c3ef9f9b69c30f87",
  "fohash": "1MRKjQwsOIlS6ZtJqKC/b0CrxduH5u29",
  "header": "HTTP/1.1 200 OK\r\nDate: Wed, 06 Apr 2022 14:43:58 GMT\r\nServer: DNVRS-Webs\r\nEtag: \"0-751-62d\"\r\nContent-Length: 1581\r\nContent-Type: text/html\r\nConnection: keep-alive\r\nKeep-Alive: timeout=60, max=99\r\nLast-Modified: Wed, 24 Apr 2013 05:37:18 GMT\r\n",
  "host": "*************:8080",
  "ip": "*************",
  "port": "8080",
  "products": [
    "HikVision-Video Monitoring"
  ],
  "server": "DNVRS-Webs",
  "timestamp": "2022-04-06 20:42:40.997361417 +0800 CST m=+5.885032313",
  "url": "/"
}
```

用jq提取关键字：
```shell script
./sutratcpdump -i en0 | jq '.client_ip + ":" + .client_port + " -> " + .host + .url, .products'
"************:55029 -> *************:8080/"
[
  "HikVision-Video Monitoring"
]
```

### 压力测试
```shell script
wrk -t8 -c100 -d30s --latency http://************:50081
```

### docker跨平台编译
```shell script
./build.sh
```

### docker调试
```shell script
docker run -it --rm -p 22345:22345 -p 32345:32345 -v /opt/gopath/src:/go/src -w /go/src golang-pcap bash -c 'cd git.gobies.org/sutra/gosutra/cmd/sutratcpdump && go build -gcflags "all=-N -l" -o sutratcpdump_linux_debug && /go/bin/dlv --listen=:22345 --headless=true --api-version=2 exec ./sutratcpdump_linux_debug -- -pprofbind :32345 -r save.cap'
```