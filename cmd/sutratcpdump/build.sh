#!/usr/bin/env bash

#WITH_DOCKER=0
#WITH_UPX=0
SRCDIR=$(cd "$(dirname "$0")";pwd)
WORKDIR=$SRCDIR/tmpbuild
OS_HOST=x86_64-linux-musl
PREFIX=$($OS_HOST-gcc -print-sysroot)
EXT=_linux

compile_linux_headers() {
#  https://github.com/sabotage-linux/kernel-headers
  if [[ "$OS_HOST" == "x86_64-linux-musl" ]]; then
      ARCH=x86_64
  elif [[ "$OS_HOST" == "mips-linux-musl" || "$OS_HOST" == "mips-linux-gnu" ]]; then
      ARCH=mips
  elif [[ "$OS_HOST" == "arm-linux-musleabi" || "$OS_HOST" == "arm-linux-gnueabi" ]]; then
      ARCH=arm
  else
      echo "no need linux headers"
      return
  fi

  cd $WORKDIR

  if [[ ! -f $PREFIX/include/linux/if.h ]]; then
    LINUX_HEADERS_TXZ=${WORKDIR}/linux-headers-4.19.88.tar.xz
    if [[ ! -f ${LINUX_HEADERS_TXZ} ]]; then
      wget http://ftp.barfooze.de/pub/sabotage/tarballs/linux-headers-4.19.88.tar.xz -O ${LINUX_HEADERS_TXZ}
    fi
    tar xvf ${LINUX_HEADERS_TXZ} -C ${WORKDIR}/
    cd ${WORKDIR}/linux-headers-4.19.88
    make ARCH=$ARCH prefix=$PREFIX install
  fi

  cd $SRCDIR
}

compile_pcap() {
  if [[ "$OS_HOST" == "x86_64-linux-musl" ]]; then
      ARCH=x86_64
  elif [[ "$OS_HOST" == "mips-linux-musl" || "$OS_HOST" == "mips-linux-gnu" ]]; then
      ARCH=mips
  elif [[ "$OS_HOST" == "arm-linux-musleabi" || "$OS_HOST" == "arm-linux-gnueabi" ]]; then
      ARCH=arm
  else
      echo "no need linux headers"
      return
  fi

  cd $WORKDIR

  if [[ ! -f $PREFIX/include/pcap/pcap.h ]]; then
    PCAPVERSION=1.9.1
    PCAPDIR=libpcap-$PCAPVERSION
    PCAPFILE=$PCAPDIR.tar.gz
    mkdir -p $WORKDIR
    if [[ ! -f $WORKDIR/$PCAPFILE ]]; then
      wget https://www.tcpdump.org/release/$PCAPFILE -O $WORKDIR/$PCAPFILE
    fi
    if [[ ! -d $WORKDIR/$PCAPDIR ]]; then
      tar xvf $WORKDIR/$PCAPFILE -C $WORKDIR/
    fi
    cd $WORKDIR/$PCAPDIR
    #      ./pcap-config --static
    ./configure --host=$OS_HOST --with-pcap=linux --enable-shared=no --prefix=$PREFIX
    make HOST="$OS_HOST" && make ARCH=$ARCH prefix=$PREFIX install
  fi

  cd $SRCDIR
}


if [[ "$WITH_DOCKER" == "1" ]]; then
    # 使用docker编译
    # shellcheck disable=SC2143
    if [[ ! $(docker images | grep golang-pcap) ]]; then
      docker build -t golang-pcap .
    fi
    docker run -it --rm -p 2345 -v /opt/gopath/src:/go/src -w /go/src golang-pcap sh -c 'cd git.gobies.org/sutra/gosutra/cmd/sutratcpdump && go build -o sutratcpdump_linux'
else
    #直接跨平台编译，需要安装pcap库
    if [[ "$OS_HOST" != "" ]]; then
        export CC=${OS_HOST}"-gcc"
        export CXX=${OS_HOST}"-g++"
    fi

    compile_linux_headers
    compile_pcap

    cd $SRCDIR
    CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -o sutratcpdump${EXT} -ldflags "-s -w -extldflags \"-static\""
fi

if [[ "$WITH_UPX" == "1" ]]; then
    upx sutratcpdump${EXT}
fi
#upx sutratcpdump_linux

#docker run -it --rm -v /opt/gopath/src:/go/src -w /go/src -e GOPROXY="https://goproxy.io,direct" golang sh -c 'apt update -y && apt install libpcap-dev -y && cd /go/src/git.gobies.org/sutra/gosutra/cmd/sutratcpdump && go build -o sutratcpdump_linux'