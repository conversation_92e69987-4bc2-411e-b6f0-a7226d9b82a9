package protocols

import (
	"git.gobies.org/sutra/gosutra/cmd/sutratcpdump/protocols/tlsparser"
)

type tlsProtocol struct {
	baseFirstPacketProtocol
}

func NewTlsProtocol(id string) Protocol {
	baseProtocol := NewBaseClientFirstProtocol(id, "tls", func(data []byte, p *baseFirstPacketProtocol) bool {
		if len(data) == 0 || data[0] != 0x16 {
			return false
		}
		extensions, err := tlsparser.GetExtensionBlock(data)
		if err != nil {
			return false
		}

		p.userData["extensions"] = extensions

		return true
	}, "func")

	return &tlsProtocol{
		baseProtocol,
	}
}

func init() {
	globalProtocolMather.Resister(NewTlsProtocol)
}
