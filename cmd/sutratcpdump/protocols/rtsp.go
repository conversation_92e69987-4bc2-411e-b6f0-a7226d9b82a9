package protocols

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/textproto"
	"net/url"
	"strconv"
	"strings"
	"sync"
)

type RtspParsedResponse struct {
	Header      string
	RawHeader   http.Header
	Server      string
	ContentType string
	Host        string
	URL         string
	Body        []byte
}

type rtspProtocol struct {
	id string

	reqs []*http.Request
	pos  int

	sync.Mutex
}

func (h *rtspProtocol) Info() map[string]interface{} {
	return map[string]interface{}{
		"protocol": "rtsp",
		"id":       h.id,
	}
}

func isRtsp(data []byte, id string) bool {
	if len(data) < 20 || !bytes.Contains(data, []byte("RTSP/")) {
		return false
	}

	index := bytes.IndexByte(data[:20], ' ')
	if index < 0 {
		return false
	}
	switch string(data[:index]) {
	case "GET", "HEAD", "POST", "PUT", "DELETE", "TRACE", "CONNECT":
		return true
		//case "HTTP/1.0", "HTTP/1.1", "HTTP/2.0":
		//	log.Println("first data is response:", id)
		//	return true
	}
	return false
}

func (h *rtspProtocol) Accept(s2c bool, data []byte) (valid bool, close bool) {
	if s2c {
		return false, false
	}
	return isRtsp(data, h.id), false
}

// parseRequestLine parses "GET /foo HTTP/1.1" into its three parts.
func parseRequestLine(line string) (method, requestURI, proto string, ok bool) {
	s1 := strings.Index(line, " ")
	s2 := strings.Index(line[s1+1:], " ")
	if s1 < 0 || s2 < 0 {
		return
	}
	s2 += s1 + 1
	return line[:s1], line[s1+1 : s2], line[s2+1:], true
}

func (h *rtspProtocol) DoClient(ctx context.Context, reader *bufio.Reader) (interface{}, error) {
	//select {
	//case <-ctx.Done():
	//	return nil, errors.New("context done")
	//default:
	//}

	req := http.Request{}
	tp := textproto.NewReader(reader)

	// First line: GET /index.html HTTP/1.0
	var s string
	var err error
	if s, err = tp.ReadLine(); err != nil {
		return nil, err
	}

	var ok bool
	req.Method, req.RequestURI, req.Proto, ok = parseRequestLine(s)
	if !ok {
		return nil, badStringError("malformed HTTP request", s)
	}
	if req.ProtoMajor, req.ProtoMinor, ok = ParseRTSPVersion(req.Proto); !ok {
		return nil, badStringError("malformed HTTP version", req.Proto)
	}

	rawurl := req.RequestURI
	justAuthority := req.Method == "CONNECT" && !strings.HasPrefix(rawurl, "/")
	if justAuthority {
		rawurl = "http://" + rawurl
	}

	if req.URL, err = url.ParseRequestURI(rawurl); err != nil {
		return nil, err
	}

	// Subsequent lines: Key: value.
	mimeHeader, err := tp.ReadMIMEHeader()
	if err != nil {
		return nil, err
	}
	req.Header = http.Header(mimeHeader)

	h.Lock()
	h.reqs = append(h.reqs, &req)
	h.Unlock()

	//log.Println(req)
	return req, nil
}

func badStringError(what, val string) error { return fmt.Errorf("%s %q", what, val) }
func ParseRTSPVersion(vers string) (major, minor int, ok bool) {
	const Big = 1000000 // arbitrary upper bound
	switch vers {
	case "RTSP/1.1":
		return 1, 1, true
	case "RTSP/1.0":
		return 1, 0, true
	}
	if !strings.HasPrefix(vers, "RTSP/") {
		return 0, 0, false
	}
	dot := strings.Index(vers, ".")
	if dot < 0 {
		return 0, 0, false
	}
	major, err := strconv.Atoi(vers[5:dot])
	if err != nil || major < 0 || major > Big {
		return 0, 0, false
	}
	minor, err = strconv.Atoi(vers[dot+1:])
	if err != nil || minor < 0 || minor > Big {
		return 0, 0, false
	}
	return major, minor, true
}

func (h *rtspProtocol) DoServer(ctx context.Context, reader *bufio.Reader) (interface{}, error) {
	var req *http.Request

	if len(h.reqs) > h.pos {
		req = h.reqs[h.pos]
	}

	tp := textproto.NewReader(reader)
	resp := &http.Response{
		Request: req,
	}

	// Parse the first line of the response.
	line, err := tp.ReadLine()
	if err != nil {
		if err == io.EOF {
			err = io.ErrUnexpectedEOF
		}
		return nil, err
	}
	if i := strings.IndexByte(line, ' '); i == -1 {
		return nil, badStringError("malformed HTTP response", line)
	} else {
		resp.Proto = line[:i]
		resp.Status = strings.TrimLeft(line[i+1:], " ")
	}
	statusCode := resp.Status
	if i := strings.IndexByte(resp.Status, ' '); i != -1 {
		statusCode = resp.Status[:i]
	}
	if len(statusCode) != 3 {
		return nil, badStringError("malformed HTTP status code", statusCode)
	}
	resp.StatusCode, err = strconv.Atoi(statusCode)
	if err != nil || resp.StatusCode < 0 {
		return nil, badStringError("malformed HTTP status code", statusCode)
	}
	var ok bool
	if resp.ProtoMajor, resp.ProtoMinor, ok = ParseRTSPVersion(resp.Proto); !ok {
		return nil, badStringError("malformed HTTP version", resp.Proto)
	}

	// Parse the response headers.
	mimeHeader, err := tp.ReadMIMEHeader()
	if err != nil {
		if err == io.EOF {
			err = io.ErrUnexpectedEOF
		}
		return nil, err
	}
	resp.Header = http.Header(mimeHeader)
	if err != nil {
		return nil, err
	}

	headerString := resp.Proto + " " + resp.Status + "\r\n"
	for k, v := range resp.Header {
		for _, v1 := range v {
			headerString += k + ": " + v1 + "\r\n"
		}
	}

	host := ""
	url := ""
	if req != nil {
		host = req.Host
		url = req.URL.String()

		// 成功了再重制偏移
		h.pos++
	} else {
		log.Println("no req of response:", h.id)
	}

	//log.Println(r)
	//log.Println("resp:", r)
	return &RtspParsedResponse{
		Header:    headerString,
		RawHeader: resp.Header,
		Server:    resp.Header.Get("Server"),
		Host:      host,
		URL:       url,
	}, nil
}

func NewRtspProtocol(id string) Protocol {
	return &rtspProtocol{
		id: id,
	}
}

func init() {
	globalProtocolMather.Resister(NewRtspProtocol)
}
