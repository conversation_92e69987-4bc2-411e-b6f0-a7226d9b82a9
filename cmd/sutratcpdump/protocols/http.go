package protocols

import (
	"bufio"
	"bytes"
	"compress/flate"
	"compress/gzip"
	"context"
	"io"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"
	"sync"
)

type httpProtocol struct {
	id string

	reqs []*http.Request
	pos  int

	sync.Mutex
}

func (h *httpProtocol) Info() map[string]interface{} {
	return map[string]interface{}{
		"protocol": "http",
		"id":       h.id,
	}
}

func isHttp(data []byte, id string) bool {
	if len(data) < 20 || !bytes.Contains(data, []byte("HTTP/")) {
		return false
	}

	index := bytes.IndexByte(data[:20], ' ')
	if index < 0 {
		return false
	}
	switch string(data[:index]) {
	case "GET", "HEAD", "POST", "PUT", "DELETE", "TRACE", "CONNECT":
		return true
		//case "HTTP/1.0", "HTTP/1.1", "HTTP/2.0":
		//	log.Println("first data is response:", id)
		//	return true
	}
	return false
}

func (h *httpProtocol) Accept(s2c bool, data []byte) (valid bool, close bool) {
	if s2c {
		return false, false
	}
	return isHttp(data, h.id), false
}

func (h *httpProtocol) DoClient(ctx context.Context, reader *bufio.Reader) (interface{}, error) {
	//select {
	//case <-ctx.Done():
	//	return nil, errors.New("context done")
	//default:
	//}

	r, err := http.ReadRequest(reader)
	if err != nil {
		return nil, err
	}
	defer r.Body.Close()

	if r.ContentLength > 0 {
		if reader.Size() < int(r.ContentLength) {
			return nil, io.EOF
		}
		_, err = readAll(r.Body, 0)
	}

	h.Lock()
	h.reqs = append(h.reqs, r)
	h.Unlock()

	//log.Println(r)
	return r, nil
}

type HttpParsedResponse struct {
	Header      string
	RawHeader   http.Header
	Server      string
	ContentType string
	Host        string
	URL         string
	Body        []byte
}

func readAll(reader io.Reader, size int) ([]byte, error) {
	if size <= 0 {
		size = int(math.Max(float64(size), 65535))
	}
	buffer := bytes.NewBuffer(make([]byte, 0, size))
	_, err := io.Copy(buffer, reader)
	if err != nil {
		return nil, err
	}
	return buffer.Bytes(), nil
}

func (h *httpProtocol) DoServer(ctx context.Context, reader *bufio.Reader) (interface{}, error) {
	var req *http.Request

	if len(h.reqs) > h.pos {
		req = h.reqs[h.pos]
	}

	r, err := http.ReadResponse(reader, req)
	//r, err := http.ReadResponse(reader, h.lastReq)
	if err != nil {
		return nil, err
	}

	defer r.Body.Close()

	contentType := r.Header.Get("Content-Type")
	contentLength := 0
	if v := r.Header.Get("Content-Length"); len(v) > 0 {
		contentLength, err = strconv.Atoi(v)
		if err != nil {
			return nil, err
		}
	}
	encoding := r.Header.Get("Content-Encoding")

	// 长度不够，直接跳出
	if contentLength > 0 && reader.Size() < contentLength {
		return nil, io.EOF
	}

	var body []byte
	// 取body
	body, err = readAll(r.Body, contentLength)
	//body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		//log.Printf("HTTP%s: failed to get body(parsed len:%d): %s\n", h.id, len(body), err)
		return nil, err
	}

	if len(contentType) == 0 {
		contentType = http.DetectContentType(body)
	}

	if strings.Contains(contentType, "html") || (len(contentType) == 0 && contentLength > 0 && contentLength < 100*1024) {
		switch encoding {
		case "gzip":
			reader, err := gzip.NewReader(bytes.NewReader(body))
			if err != nil {
				log.Printf("Failed to gzip decode: %s", err)
				return nil, err
			}
			body, err = readAll(reader, 0)
			if err != nil {
				log.Printf("HTTP%s: failed to get body(parsed len:%d): %s\n", h.id, len(body), err)
				return nil, err
			}
		case "deflate":
			reader1 := flate.NewReader(bytes.NewReader(body))
			body, err = readAll(reader1, 0)
			if err != nil {
				log.Printf("HTTP%s: failed to get body(parsed len:%d): %s\n", h.id, len(body), err)
				return nil, err
			}
			reader1.Close()
		default:
			//reader = bytes.NewBuffer(body)
			//reader = bytes.NewReader(body)
		}
	}

	headerString := r.Proto + " " + r.Status + "\r\n"
	for k, v := range r.Header {
		for _, v1 := range v {
			headerString += k + ": " + v1 + "\r\n"
		}
	}

	host := ""
	url := ""
	if req != nil {
		host = req.Host
		url = req.URL.String()

		// 成功了再重制偏移
		h.pos++
	} else {
		log.Println("no req of response:", h.id)
	}

	//log.Println(r)
	//log.Println("resp:", r)
	return &HttpParsedResponse{
		ContentType: contentType,
		Header:      headerString,
		RawHeader:   r.Header,
		Body:        body,
		Server:      r.Header.Get("Server"),
		Host:        host,
		URL:         url,
	}, nil
}

func NewHTTPProtocol(id string) Protocol {
	return &httpProtocol{
		id: id,
	}
}

func init() {
	globalProtocolMather.Resister(NewHTTPProtocol)
}
