package protocols

import (
	"git.gobies.org/sutra/gosutra/cmd/sutratcpdump/protocols/tlsparser"
)

type httpsProtocol struct {
	baseFirstPacketProtocol
}

func NewHttpsProtocol(id string) Protocol {
	baseProtocol := NewBaseClientFirstProtocol(id, "https", func(data []byte, p *baseFirstPacketProtocol) bool {
		if hostname, err := tlsparser.GetHostname(data[:]); err == nil && len(hostname) > 0 {
			p.userData["host"] = hostname
			return true
		}
		return false
	}, "func")

	return &httpsProtocol{
		baseProtocol,
	}
}

func init() {
	globalProtocolMather.Resister(NewHttpsProtocol)
}
