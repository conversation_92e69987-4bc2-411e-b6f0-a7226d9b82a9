# gosutra

golang语言的sutra开发包

[![coverage report](https://git.gobies.org/sutra/gosutra/badges/main/coverage.svg)](https://git.gobies.org/sutra/gosutra/-/jobs/artifacts/main/browse?job=coverage)
![build](https://git.gobies.org/sutra/gosutra/badges/main/pipeline.svg)

## 功能列表
- 支持本地原生的本地规则引擎

## 查看文档
```shell script
godoc -http=:6060 -play
```
然后访问：http://127.0.0.1:6060/pkg/git.gobies.org/sutra/gosutra/rulengine/

## 修改
合并所有的"高恪网络"规则，通过正则可以表达：
```json
{"product":"GoCloud-router","rule":"protocol=\"telnet\" && banner~=\"GOCLOUD .*? series router\"","rule_id":"758526","level":"1","category":"路由器","parent_category":"网络产品","softhard":"1","company":"上海国云信息科技有限公司"}

```

```sql
# 复制taged
update rules set sutra=1 where taged=1;
# 去掉高恪网络
update rules set sutra=0 where sutra=1 and product like '%高恪网络%';
# 新增高恪网络的正则规则
insert into rules (product, producturl, rule, company, country_code, level_code, soft_hard_code, en_product, en_company, sutra, dup_fetch) values ('高恪网络-路由设备', 'http://www.gocloud.cn/', 'banner="GOCLOUD .*? series router"', '上海国云信息科技有限公司', 1, 1, 1, 'GoCloud-Device', 'Shanghai Guoyun Information Technology Co., Ltd.', 1, 1)
# 去掉现网没有数据，且非价值型的规则
//
```