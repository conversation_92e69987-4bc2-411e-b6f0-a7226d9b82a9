package algo

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestEncryptStringToBase64(t *testing.T) {
	assert.Equal(t, "cj6zAcldQcI=", EncryptStringToBase64("abc", ""))
	assert.Equal(t, "JW3gGZEaXFcF4s/H6N75+w==", EncryptStringToBase64("abcd12345", ""))

	assert.Equal(t, "U5qfZbwhO/s=", EncryptStringToBase64("abc", "fofafofa"))
	assert.Equal(t, "0/QDhQxwY6xu9BOUESr+HQ==", EncryptStringToBase64("abcd12345", "fofafofa"))

}

func TestDecryptFromBase64(t *testing.T) {
	assert.Equal(t, "abc", mustDecryptFromBase64("cj6zAcldQcI=", ""))
	assert.Equal(t, "abcd12345", mustDecryptFromBase64("JW3gGZEaXFcF4s/H6N75+w==", ""))

	assert.Equal(t, "abc", mustDecryptFromBase64("U5qfZbwhO/s=", "fofafofa"))
	assert.Equal(t, "abcd12345", mustDecryptFromBase64("0/QDhQxwY6xu9BOUESr+HQ==", "fofafofa"))

	assert.Equal(t, "e76d3debeb6565a8c3ef9f9b69c30f87", SignString(mustDecryptFromBase64("1MRKjQwsOIlS6ZtJqKC/b0CrxduH5u29", "fofafofa")))
}
