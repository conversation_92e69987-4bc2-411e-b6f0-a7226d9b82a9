package algo

import (
	"bytes"
	"crypto/cipher"
	"crypto/des"
	"encoding/base64"
	"errors"
)

var (
	ErrPaddingSize = errors.New("padding size error please check the secret key or iv")
)

func fPKCS5Padding(plainText []byte, blockSize int) []byte {
	padding := blockSize - (len(plainText) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	newText := append(plainText, padText...)
	return newText
}

func fPKCS5UnPadding(plainText []byte) ([]byte, error) {
	length := len(plainText)
	number := int(plainText[length-1])
	if number > length {
		return nil, ErrPaddingSize
	}
	return plainText[:length-number], nil
}

// encryptString 加密
// iv 直接用key
func encrypt(plainText []byte, key []byte) ([]byte, error) {
	block, err := des.NewCipher(key)
	if err != nil {
		return nil, err
	}
	paddingText := fPKCS5Padding(plainText, block.BlockSize())
	blockMode := cipher.NewCBCEncrypter(block, key)

	cipherText := make([]byte, len(paddingText))
	blockMode.CryptBlocks(cipherText, paddingText)
	return cipherText, nil
}

func decrypt(cipherText []byte, key []byte) ([]byte, error) {
	block, err := des.NewCipher(key)
	if err != nil {
		return nil, err
	}
	blockMode := cipher.NewCBCDecrypter(block, key)
	origData := make([]byte, len(cipherText))
	blockMode.CryptBlocks(origData, cipherText)
	return fPKCS5UnPadding(origData)
}

// EncryptStringToBase64 加密到base64的string
// key 可以留空
func EncryptStringToBase64(plainText string, key string) string {
	if len(key) == 0 {
		key = MagicKey
	}
	v, err := encrypt([]byte(plainText), []byte(key))
	if err != nil {
		panic(err)
	}
	return base64.StdEncoding.EncodeToString(v)
}

func DecryptFromBase64(cipherText string, key string) (string, error) {
	if len(key) == 0 {
		key = MagicKey
	}

	rawData, err := base64.StdEncoding.DecodeString(cipherText)
	if err != nil {
		return "", err
	}

	d, err := decrypt(rawData, []byte(key))
	if err != nil {
		return "", err
	}
	return string(d), nil
}

func mustDecryptFromBase64(cipherText string, key string) string {
	d, err := DecryptFromBase64(cipherText, key)
	if err != nil {
		panic(err)
	}
	return d
}
