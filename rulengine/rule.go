package rulengine

import (
	"git.gobies.org/sutra/gosutra/structs"
	parsec "github.com/prataprc/goparsec"
	"log"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
)

var (
	EnableCacheRegex bool // 是否开启rule的正则缓存，不开启就慢但是省内存，开启就快但是费内存
	lastID           int32
)

type RuleT struct {
	Name     string   // 名称
	Content  string   // 内容
	Fields   []string // 使用到的字段名称列表
	fieldstr string
	Type     structs.ObjectType // 类型
	ParseOk  bool
	regCache sync.Map // regex缓存
	id       uint64
	scans    *ScanTest // 解析后的执行器
}

func newRuleFromLine(line string) (*RuleT, error) {
	kv := strings.SplitN(line, ":", 2)
	return NewRule(kv[0], kv[1])
}

func genId() int32 {
	return atomic.AddInt32(&lastID, 1000000)
}

func NewRule(name, content string) (*RuleT, error) {
	r := &RuleT{
		Name:    name,
		Content: content,
		id:      uint64(genId()),
	}

	if err := r.parse(); err != nil {
		return nil, err
	}
	return r, nil
}

func isSubdomianField(field string) bool {
	if field == "body" || field == "title" || field == "domain" || field == "header" || field == "icon_hash" {
		return true
	}
	return false
}
func isServiceField(field string) bool {
	if field == "banner" || field == "protocol" {
		return true
	}
	return false
}

func (r *RuleT) IsSubdomain() bool {
	return r.Type == structs.OtSubdomain
}

func (r *RuleT) parse() (err error) {
	defer func() {
		if rc := recover(); rc != nil {
			if e, ok := rc.(error); ok {
				err = e
			}
		}
	}()

	isService := false
	isSubdomain := false

	nodes := parseToNodes(r.Content)
	r.scans = parseTests(nodes, func(field string) {
		exists := false
		for _, f := range r.Fields {
			if f == field {
				exists = true
				break
			}
		}
		if !exists {
			r.Fields = append(r.Fields, field)
		}
	})

	for _, v := range r.Fields {
		if isSubdomianField(v) {
			isSubdomain = true
			r.Type = structs.OtSubdomain
		}
		if isServiceField(v) {
			isService = true
			r.Type = structs.OtService
		}
	}
	r.fieldstr = strings.Join(r.Fields, ",")

	// 需要检查冲突
	if isSubdomain && isService {
		//log.Println("[WARNING] either service and subdomain type exists:", r.Fields)
		//return errors.New("either service and subdomain type exists")
		r.Type = structs.OtMixed
	}

	r.ParseOk = true
	return nil
}

// 解析出来left，operation，right
func parseLOR(q []parsec.Queryable) (left, operation, right parsec.Queryable) {
	state := 0 //left operation right
	lor := [3]parsec.Queryable{}

	for _, node := range q {
		k := node.GetName()
		//v := node.GetValue()

		if k == "left_parenthesis" {
			lor[state] = node
			state++
		} else if k == "right_parenthesis" {
			lor[state] = node
			state++
		} else if k == "missing" {

		} else {
			lor[state] = node
			state++
		}
	}
	left, operation, right = lor[0], lor[1], lor[2]
	return
}

func (r *RuleT) fieldName(name string) (string, bool) {
	if !strings.Contains(r.fieldstr, name) {
		return "", false
	}
	return name, true
}

func unEscapeString(rv string) string {
	if len(rv) == 0 || rv[0] != '"' {
		return rv
	}

	if !strings.Contains(rv, `\`) {
		return rv[1 : len(rv)-1]
	}

	newstr, err := strconv.Unquote(rv)
	if err != nil {
		panic(err)
	}
	return newstr
}

// 需要测试
// 1，中文
// 2，\\ 和 \"
func parseValue(q parsec.Queryable) string {
	if q.GetName() != "DOUBLEQUOTESTRING" {
		return q.GetValue()
	}
	rv := q.GetValue()
	return unEscapeString(rv)
}

func (r *RuleT) stringEq(it interface{}, fieldName string, checkValue string) bool {
	obj := it.(*structs.JsonObj)
	field, exists := r.fieldName(fieldName)
	if !exists {
		return false
	}
	checkValue = strings.ToLower(checkValue)
	value := obj.StringOfField(field)
	return strings.Contains(value, checkValue)
}

func (r *RuleT) stringFullEq(it interface{}, fieldName string, checkValue string) bool {
	obj := it.(*structs.JsonObj)
	field, exists := r.fieldName(fieldName)
	if !exists {
		return false
	}
	value := obj.GetString(field)
	return value == checkValue
}

func (r *RuleT) regexMatches(it interface{}, fieldName string, checkValue string) bool {
	obj := it.(*structs.JsonObj)
	field, exists := r.fieldName(fieldName)
	if !exists {
		return false
	}
	value := obj.GetString(field)
	var reg *regexp.Regexp
	if EnableCacheRegex {
		v, _ := r.regCache.LoadOrStore(checkValue, regexp.MustCompile(checkValue))
		reg = v.(*regexp.Regexp)
	} else {
		reg = regexp.MustCompile(checkValue)
	}
	return reg.MatchString(value)
}

type scanTestType int
type scanTestOperator int

const (
	stNone  scanTestType = 0
	stItem  scanTestType = 1
	stGroup scanTestType = 2

	soNone     scanTestOperator = 0
	soEq       scanTestOperator = 1
	soFullEq   scanTestOperator = 2
	soNotEq    scanTestOperator = 3
	soRegMatch scanTestOperator = 4
	soOr       scanTestOperator = 5
	soAnd      scanTestOperator = 6
)

type ScanTest struct {
	Type      scanTestType // group, item
	Operation scanTestOperator
	Field     string
	Value     string
	Children  []*ScanTest
}

func operatorId(o string) scanTestOperator {
	switch o {
	case "eq":
		return soEq
	case "fulleq":
		return soFullEq
	case "not_eq":
		return soNotEq
	case "matches":
		return soRegMatch
	case "and":
		return soAnd
	case "or":
		return soOr
	default:
		log.Fatalln("unknown operator", o)
	}
	return soNone
}

func ParseQuery(query string, onField func(field string)) *ScanTest {
	return parseTests(parseToNodes(query), onField)
}

func parseTests(q []parsec.Queryable, onField func(field string)) *ScanTest {
	left, operation, right := parseLOR(q)

	if operation != nil {
		if operation.GetName() == "and_operator" || operation.GetName() == "or_operator" {
			return &ScanTest{
				Type:      stGroup,
				Operation: operatorId(strings.Split(operation.GetName(), "_")[0]),
				Children: []*ScanTest{
					parseTests(left.GetChildren(), onField),
					parseTests(right.GetChildren(), onField),
				},
			}
		}
	}

	for _, node := range q {
		switch node.GetName() {
		case "missing":
			continue
		case "left_parenthesis":
			//ret += "("
		case "right_parenthesis":
			//ret += ")"
		case "parenthesis":
			return parseTests(node.GetChildren(), onField)
		case "compare_eq", "compare_not_eq", "compare_matches", "compare_fulleq",
			"compare_lt", "compare_lteq", "compare_gt", "compare_gteq":
			return parseTests(node.GetChildren(), onField)
		case "eq", "not_eq", "matches", "fulleq":
			field := left.GetValue()
			if onField != nil {
				onField(field)
			}
			return &ScanTest{
				Type:      stItem,
				Operation: operatorId(node.GetName()),
				Field:     field,
				Value:     parseValue(right),
			}
		//case "lt":
		//	ret += fqp.processMathToYara(left, right, "<")
		//case "lteq":
		//	ret += fqp.processMathToYara(left, right, "<=")
		//case "gt":
		//	ret += fqp.processMathToYara(left, right, ">")
		//case "gteq":
		//	ret += fqp.processMathToYara(left, right, ">=")
		case "and_operation_item", "or_operation_item":
			return parseTests(operation.GetChildren(), onField)
		//case "or_operator":
		//	ret += "(" + fqp.parseAstToYara(left.GetChildren()) + " or " + fqp.parseAstToYara(right.GetChildren()) + ")"
		case "literal", "number", "DOUBLEQUOTESTRING":
			if node == left && right == nil && operation == nil {
				//模糊查询
				return &ScanTest{
					Type:      stGroup,
					Operation: soOr,
					Children: []*ScanTest{
						{
							Type:      stItem,
							Operation: soEq,
							Field:     "body",
							Value:     node.GetValue(),
						},
						{
							Type:      stItem,
							Operation: soEq,
							Field:     "header",
							Value:     node.GetValue(),
						},
						{
							Type:      stItem,
							Operation: soEq,
							Field:     "server",
							Value:     node.GetValue(),
						},
						{
							Type:      stItem,
							Operation: soEq,
							Field:     "banner",
							Value:     node.GetValue(),
						},
						{
							Type:      stItem,
							Operation: soEq,
							Field:     "title",
							Value:     node.GetValue(),
						},
					},
				}
			}
		case "null", "true", "false":
		default:
			log.Println("[WARNING] Unknown operation of", node.GetName())
		}
	}
	return nil
}

func DoCheck(obj interface{}, scan *ScanTest,
	stringEq func(obj interface{}, fieldName string, checkValue string) bool,
	stringFullEq func(obj interface{}, fieldName string, checkValue string) bool,
	regexMatches func(obj interface{}, fieldName string, checkValue string) bool) bool {
	switch scan.Type {
	case stGroup:
		switch scan.Operation {
		case soAnd:
			for _, s := range scan.Children {
				if !DoCheck(obj, s, stringEq, stringFullEq, regexMatches) {
					return false
				}
			}
			return true
		case soOr:
			for _, s := range scan.Children {
				if DoCheck(obj, s, stringEq, stringFullEq, regexMatches) {
					return true
				}
			}
			return false
		default:
			log.Fatalln("unknown operation:", scan.Operation)
		}
	case stItem:
		switch scan.Operation {
		case soEq:
			return stringEq(obj, scan.Field, scan.Value)
		case soFullEq:
			return stringFullEq(obj, scan.Field, scan.Value)
		case soRegMatch:
			return regexMatches(obj, scan.Field, scan.Value)
		case soNotEq:
			return !stringEq(obj, scan.Field, scan.Value)
		default:
			log.Fatalln("unknown operation:", scan.Operation)
		}
	default:
		log.Fatalln("unknown type:", scan.Type)
	}
	return false
}

/*
Check 测试是否匹配规则，注意：这里没有对obj进行归一化处理
*/
func (r *RuleT) Check(obj *structs.JsonObj) bool {
	if r.scans == nil {
		r.parse()
	}
	return DoCheck(obj, r.scans, r.stringEq, r.stringFullEq, r.regexMatches)
}
