package rulengine

import (
	"context"
	"fmt"
	"git.gobies.org/sutra/gosutra/structs"
	"github.com/stretchr/testify/assert"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"testing"
)

func ExampleLoad() {
	ctx := context.Background()
	loadOk := Load(ctx, `{"product":"test","rule":"banner=test"}`, true)
	fmt.Println(loadOk)
	fmt.Println(RuleSize())
	loadOk = Load(ctx, `{"product":"test","rule":"banner=test"}`, true)
	fmt.Println(loadOk)
	fmt.Println(RuleSize())
	loadOk = Load(ctx, `{"product":"test1","rule":"banner=test"}`, false)
	fmt.Println(loadOk)
	fmt.Println(RuleSize())
	loadOk = Load(ctx, `-`, false)
	fmt.Println(loadOk)
	fmt.Println(RuleSize())
	// Output:
	// true
	// 1
	// true
	// 1
	// true
	// 2
	// false
	// 2
}

var (
	testRule = `{"product":"jQuery","rule":"body=\"jquery\"","rule_id":"138","level":"4","category":"Middleware","parent_category":"Support System","softhard":"2","company":"The jQuery Foundation."}`
)

func checkRuleValid(t *testing.T) {
	obj, err := structs.NewJsonObj(`{
  "host": "************:19999",
  "ip": "************",
  "port": "19999",
  "header": "HTTP/1.1 200 OK\r\nConnection: close\r\nContent-Length: 5683\r\nAccess-Control-Allow-Headers: Origin, Content-Type, X-Auth-Token\r\nAccess-Control-Allow-Methods: GET, POST, PATCH, PUT, DELETE, OPTIONS\r\nAccess-Control-Allow-Origin: *\r\nCache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0\r\nCache-Control: no-cache\r\nContent-Type: text/html; charset=UTF-8\r\nDate: Tue, 07 Dec 2021 08:38:44 GMT\r\nExpires: Thu, 19 Nov 1981 08:52:00 GMT\r\nPragma: no-cache\r\nServer: Apache/2.4.18 (Ubuntu)\r\nSet-Cookie: PHPSESSID=4qijsihdtm0f5i2g9sp0b3epm1; path=/\r\nSet-Cookie: laravel_session=eyJpdiI6IlFFYkxcL2RwM0FEaCtVVUI1Y3pyOGxBPT0iLCJ2YWx1ZSI6IjhRSXhyN3NSYlhqV1llMFwvZVJUQXA0WnlwVDhRc3lRQXVNXC9ZSlVvRERhVHNFXC8yTEk1WTE4ZWs4UjR5K3hEYjFaRVlRZ3laSWZXUmlicHlDS0VCbXNRPT0iLCJtYWMiOiIxYTc2NGQ2ZTgyNGIzMzc3OThiMDhkNTgxNmIxNWZlOWNhNTQ2ZjkxN2QxMzYxYjZlYzhlZGY3ZDI0N2ZkZDQ1In0%3D; expires=Tue, 07-Dec-2021 10:38:44 GMT; Max-Age=7200; path=/; httponly\r\nVary: Accept-Encoding\r\n",
  "body": "<!DOCTYPE html>\n<html>\n\n<head>\n\n    <meta charset=\"utf-8\">\n    <meta name=\"csrf-token\" content=\"ssPrHF40slnKo65EdL0QGFZOnsILMKHChOU1xk6T\" />\n    <title>SISTER</title>\n    <link rel=\"icon\" type=\"image/png\" href=\"http://************:19999/favicon.png\"/>\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n    <title>INSPINIA | Login 2</title>\n\n    <link href=\"http://************:19999/css/bootstrap.min.css\" rel=\"stylesheet\">\n    <link href=\"http://************:19999/font-awesome/css/font-awesome.min.css\" rel=\"stylesheet\">\n    <!-- <link href=\"http://************:19999/css/animate.css\" rel=\"stylesheet\"> -->\n    <link rel=\"stylesheet\" href=\"http://************:19999/bower_components/sweetalert/dist/sweetalert.css\">\n    <script type=\"text/javascript\" src=\"http://************:19999/js/jquery-2.1.1.js\"></script>\n\n    <link href=\"http://************:19999/css/plugins/toastr/toastr.min.css\" rel=\"stylesheet\">\n\t<script src=\"http://************:19999/js/plugins/toastr/toastr.min.js\"></script>\n\n\t<style type=\"text/css\">\n\t\t#toast-container > .toast-error:before{\n\t\t\tcontent: '';\n\t\t}\n\t</style>\n\t\n\t<script type=\"text/javascript\">\n\t\t$(function () {\n\t\t\ttoastr.options = {\n\t\t\t  \"closeButton\": true,\n\t\t\t  \"debug\": false,\n\t\t\t  \"progressBar\": true,\n\t\t\t  \"preventDuplicates\": false,\n\t\t\t  \"positionClass\": \"toast-top-right\",\n\t\t\t  \"onclick\": null,\n\t\t\t  \"showDuration\": 400,\n\t\t\t  \"hideDuration\": 1000,\n\t\t\t  \"timeOut\": 7000,\n\t\t\t  \"extendedTimeOut\": 1000,\n\t\t\t  \"showEasing\": \"swing\",\n\t\t\t  \"hideEasing\": \"linear\",\n\t\t\t  \"showMethod\": \"fadeIn\",\n\t\t\t  \"hideMethod\": \"fadeOut\"\n\t\t\t}\n\t\t});\n\t</script>\n    <link href=\"http://************:19999/css/plugins/iCheck/custom.css\" rel=\"stylesheet\">\n<script src=\"http://************:19999/js/plugins/iCheck/icheck.min.js\"></script>\n\n<script type=\"text/javascript\">\n\tvar initialize_icheck = function(){\n\t\t$('.i-checks').iCheck({\n            checkboxClass: 'icheckbox_square-green',\n            radioClass: 'iradio_square-green',\n        });\n\t}\n\n    $(document).ready(function () {\n    \tinitialize_icheck();    \n    });\n</script>    \n\n    <link href=\"http://************:19999/eak/ejs/css/classy.css\" rel=\"stylesheet\">\n    <link href=\"http://************:19999/css/style.css\" rel=\"stylesheet\">\n    <link href=\"http://************:19999/css/login.css\" rel=\"stylesheet\">\n\n\n</head>\n\n<body class=\"gray-bg bg-cover\" style=\"background: url(http://************:19999/bg_login_new.jpg) no-repeat fixed;;\">\n\n    \n    <div class=\"middle-box animated fadeInDown\">\n        <div class=\"abs-bg\"></div>\n\n        <div class=\"content loginscreen\">\n            <div class=\"text-center\">\n                <div>\n                    <img src=\"http://************:19999/logo.png\" class=\"logo\" />\n                </div>\n                <p class=\"new-logo-name\" style=\"margin: 0px\">\n                    SISTER<br>\n                    <p style=\"font-size: small;\"><b>Sistem Informasi Sumberdaya Terintegrasi</b></p>\n                </p>\n                                    <h3><b>IAIN Ambon</b></h3>\n                            </div>\n\n                <form method=\"post\" action=\"http://************:19999/auth/login\" class=\"m-t\" role=\"form\">\n        <input type=\"hidden\" name=\"_token\" value=\"ssPrHF40slnKo65EdL0QGFZOnsILMKHChOU1xk6T\">\n        \n        <div class=\"form-group \">\n            <label class=\"control-label\">USERNAME</label>\n            <input type=\"text\" name=\"username\" value=\"\" class=\"form-control\" placeholder=\"Tulis username/email anda...\">\n        </div>\n        <div class=\"form-group  \">\n            <label class=\"control-label\">PASSWORD</label>\n            <input type=\"password\" name=\"password\" id=\"password\" class=\"form-control\" placeholder=\"Tulis password anda...\">\n        </div>\n        \n        <button type=\"submit\" class=\"btn btn-success block full-width m-b noborder-radius\"><b>LOGIN</b></button>\n\n        <table class=\"table\">\n            <tbody>\n                <tr>\n                    <td>\n                        <div style=\"text-align: left;\">\n                            <label class=\"control-label\">\n                                <h5><a href=\"http://************:19999/password/lupa\">Lupa password?</a></h5>\n                            </label>\n                        </div>\n                    </td>\n                    <td>\n                        <div style=\"text-align: right;\">\n                            <label class=\"control-label\">\n                                <h5>Belum memiliki akun?<a href=\"http://************:19999/registrasi\"> Daftar di sini.</a></h5>\n                            </label>\n                        </div>\n                    </td>\n                </tr>\n                <tr>\n                    <td>\n                        <div style=\"text-align: left;\"><h4><a href=\"http://************:19999/panduan\" target=\"_blank\" ><i class=\"fa fa-download\"></i> Unduh Panduan</a></h4></td>\n                            </div>\n                    <td><div style=\"text-align: right;\">\n                        <span style=\"font-size: 0.7em\">\n                    <i class=\"fa fa-envelope\"></i>\n                </span>\n                <span style=\"font-size: 0.75em\">\n                    <EMAIL>\n                </span>\n                    </div></td>\n                </tr>\n            </tbody>\n        </table>\n    </form>\n\n            <p class=\"m-t text-center\"> \n                <small>\n                    <b>Direktorat Sumber Daya - Direktorat Jenderal Pendidikan Tinggi, Riset dan Teknologi</b>\n                </small>\n            </p>\n        </div>\n    </div>\n    <script src=\"http://************:19999/bower_components/sweetalert/dist/sweetalert.min.js\"></script>\n</body>\n\n</html>\n",
  "nhash": "-4246898293106848714",
  "ehash": "0aa2f3630eda43fe20d7ef9458e0bea5",
  "lastupdatetime": "2021-12-07 07:40:42"
}`)
	assert.Nil(t, err)
	ps, err := Products(obj)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(ps))
}

func TestLoadFromFile(t *testing.T) {
	tmpfile, err := ioutil.TempFile("", "example")
	assert.Nil(t, err)
	defer os.Remove(tmpfile.Name()) // clean up

	_, err = tmpfile.WriteString(testRule)
	assert.Nil(t, err)
	err = tmpfile.Close()
	assert.Nil(t, err)

	_, err = LoadFromFile(context.Background(), tmpfile.Name(), true)
	assert.Nil(t, err)
	assert.Equal(t, 1, RuleSize())
	checkRuleValid(t)
}

func TestProducts(t *testing.T) {
	loadOk := Load(context.Background(), testRule, true)
	assert.True(t, loadOk)
	checkRuleValid(t)

	loadOk = Load(context.Background(), `{"product":"Apache-Web-Server","rule":"((header=\"Server: httpd\" || (server=\"Apache\" \u0026\u0026 header!=\"Apache-Coyote\")) \u0026\u0026 header!=\"couchdb\" \u0026\u0026 header!=\"drupal\" \u0026\u0026 body!=\"\u003ch2\u003eMy Resource\u003c/h2\u003e\" \u0026\u0026 body!=\"Server: CouchDB\" \u0026\u0026 header!=\"ReeCam IP Camera\" \u0026\u0026 header!=\"Apache,Tomcat,Jboss\") || (banner=\"Server: httpd\" || (banner=\"Server: Apache\" \u0026\u0026 banner!=\"Apache-Coyote\") \u0026\u0026 banner!=\"couchdb\" \u0026\u0026 banner!=\"drupal\" \u0026\u0026 banner!=\"ReeCam IP Camera\")","rule_id":"211","level":"3","category":"Service","parent_category":"Support System","softhard":"2","company":"Apache Software Foundation."}`, true)
	assert.True(t, loadOk)
	d := `{
  "host": "************:9009",
  "ip": "************",
  "port": "9009",
  "header": "HTTP/1.0 200 Ok\r\nCache-Control: no-store, no-cache, must-revalidate\r\nCache-Control: post-check=0, pre-check=0\r\nCache-Control: no-cache\r\nConnection: close\r\nContent-Type: text/html\r\nDate: Sun, 22 Nov 2020 02:29:11 GMT\r\nExpires: 0\r\nPragma: no-cache\r\nPragma: no-cache\r\nServer: httpd\r\n",
  "body": "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Strict//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd\">\n<html>\n\t<head>\n\t\t<meta http-equiv=\"Content-Type\" content=\"application/xhtml+xml; charset=iso-8859-1\" />\n\t\t<link rel=\"icon\" href=\"images/favicon.ico\" type=\"image/x-icon\" />\n\t\t<link rel=\"shortcut icon\" href=\"images/favicon.ico\" type=\"image/x-icon\" />\n\t\t<script type=\"text/javascript\" src=\"common.js\"></script>\n\t\t<script type=\"text/javascript\" src=\"lang_pack/english.js\"></script>\n\t\t<script type=\"text/javascript\" src=\"lang_pack/language.js\"></script>\n\t\t<link type=\"text/css\" rel=\"stylesheet\" href=\"style/yellow/style.css\" />\n\t\t<!--[if IE]><link type=\"text/css\" rel=\"stylesheet\" href=\"style/yellow/style_ie.css\" /><![endif]-->\n\t\t<script type=\"text/javascript\" src=\"js/prototype.js\"></script>\n\t\t<script type=\"text/javascript\" src=\"js/effects.js\"></script>\n\t\t<script type=\"text/javascript\" src=\"js/window.js\"></script>\n\t\t<script type=\"text/javascript\" src=\"js/window_effects.js\"></script>\n\t\t<link type=\"text/css\" rel=\"stylesheet\" href=\"style/pwc/default.css\" />\n\t\t<link type=\"text/css\" rel=\"stylesheet\" href=\"style/pwc/ddwrt.css\" />\n\t\t<title>DD-WRT (build 21061) - Info</title>\n\n<script type=\"text/javascript\">\r\n//<![CDATA[\nfunction setWirelessTable() {\nvar table = document.getElementById(\"wireless_table\");\nvar val = arguments;\nif (!table)\nreturn;\ncleanTable(table);\nif(!val.length) {\nvar cell = table.insertRow(-1).insertCell(-1);\ncell.colSpan = 10;\ncell.align = \"center\";\ncell.innerHTML = \"- \" + share.none + \" -\";\nreturn;\n}\nfor(var i = 0; i < val.length; i = i + 9) {\nvar row = table.insertRow(-1);\n\nvar mac = val[i];\nif (\"1\" != \"1\") {\nvar cellmac = row.insertCell(-1);\ncellmac.title = share.oui;\ncellmac.style.cursor = \"pointer\";\ncellmac.style.textDecoration = \"underline\";\neval(\"addEvent(cellmac, 'click', function() { getOUIFromMAC('\" + mac + \"') })\");\ncellmac.innerHTML = mac;\n} else {\nrow.insertCell(-1).innerHTML = mac;\t\t\r\n}\nvar ifn = val[i + 1];\nvar iface = row.insertCell(-1);\niface.title = status_band.titl;\niface.style.cursor = \"pointer\";\niface.style.textDecoration = \"none\";\neval(\"addEvent(iface, 'click', function() { openBW('\" + ifn + \"') })\");\niface.innerHTML = ifn;\n\nrow.insertCell(-1).innerHTML = val[i + 2];\nrow.insertCell(-1).innerHTML = val[i + 3];\nrow.insertCell(-1).innerHTML = val[i + 4];\t\t\nrow.insertCell(-1).innerHTML = val[i + 5];\t\t\nrow.insertCell(-1).innerHTML = val[i + 6];\t\t\nrow.insertCell(-1).innerHTML = val[i + 7];\t\t\nsetMeterBar(row.insertCell(-1), (val[i + 8] == \"0\" ? 0 : parseInt(val[i + 8]) * 0.1), \"\");\n}\n}\nfunction setWDSTable() {\nvar table = document.getElementById(\"wds_table\");\nvar val = arguments;\ncleanTable(table);\nif(!val.length) {\nsetElementVisible(\"wds\", false);\nreturn;\n}\nfor(var i = 0; i < val.length; i = i + 6) {\nvar row = table.insertRow(-1);\n\nvar mac = val[i];\nif (\"1\" != \"1\") {\nvar cellmac = row.insertCell(-1);\ncellmac.title = share.oui;\ncellmac.style.cursor = \"pointer\";\ncellmac.style.textDecoration = \"underline\";\neval(\"addEvent(cellmac, 'click', function() { getOUIFromMAC('\" + mac + \"') })\");\ncellmac.innerHTML = mac;\n} else {\nrow.insertCell(-1).innerHTML = mac;\t\t\r\n}\nvar ifn = val[i + 1];\nvar iface = row.insertCell(-1);\niface.title = status_band.titl;\niface.style.cursor = \"pointer\";\niface.style.textDecoration = \"none\";\neval(\"addEvent(iface, 'click', function() { openBW('\" + ifn + \"') })\");\niface.innerHTML = ifn;\n\nrow.insertCell(-1).innerHTML = val[i + 2];\nrow.insertCell(-1).innerHTML = val[i + 3];\nrow.insertCell(-1).innerHTML = val[i + 4];\nrow.insertCell(-1).innerHTML = val[i + 5];\nsetMeterBar(row.insertCell(-1), (val[i + 3] == \"0\" ? 0 : parseInt(val[i + 3]) * 1.24 + 116), \"\");\n}\nsetElementVisible(\"wds\", true);\n}\nfunction setDHCPTable() {\nvar table = document.getElementById(\"dhcp_leases_table\");\nvar val = arguments;\ncleanTable(table);\nif(!val.length) {\nvar cell = table.insertRow(-1).insertCell(-1);\ncell.colSpan = 4;\ncell.align = \"center\";\ncell.innerHTML = \"- \" + share.none + \" -\";\nreturn;\n}\nfor(var i = 0; i < val.length; i = i + 5) {\n\nvar row = table.insertRow(-1);\nrow.insertCell(-1).innerHTML = val[i];\nrow.insertCell(-1).innerHTML = val[i + 1];\n\nvar mac = val[i + 2];\nif (\"1\" != \"1\") {\nvar cellmac = row.insertCell(-1);\ncellmac.title = share.oui;\ncellmac.style.cursor = \"pointer\";\ncellmac.style.textDecoration = \"underline\";\neval(\"addEvent(cellmac, 'click', function() { getOUIFromMAC('\" + mac + \"') })\");\ncellmac.innerHTML = mac;\n} else {\nrow.insertCell(-1).innerHTML = mac;\t\t\r\n}\nrow.insertCell(-1).innerHTML = val[i + 3];\n}\n}\nfunction setPacketInfo(val) {\nvar packet = val.replace(/[A-Za-z=]/g, \"\").split(\";\");\nsetElementContent(\"packet_rx\", packet[0] + \" OK, \" + (packet[1] > 0 ? packet[1] + \" \" + share.errs : share.none2 + \" \" + share.err) );\nsetElementContent(\"packet_tx\", packet[2] + \" OK, \" + (packet[3] > 0 ? packet[3] + \" \" + share.errs : share.none2 + \" \" + share.err) );\n}\nfunction setMemoryValues(val) {\nvar mem = val.replace(/'/g, \"\").split(\",\");\nvar memTotal = parseInt(mem[19]) / 1024;\nvar memSystem = Math.pow(2, Math.ceil(Math.log(memTotal) / Math.LN2));\nvar memFree = parseInt(mem[22]) / 1024;\nvar memUsed = memTotal - memFree;\nvar memBuffer = parseInt(mem[28]) / 1024;\nvar memCached = parseInt(mem[31]) / 1024;\nvar memActive = parseInt(mem[37]) / 1024;\nvar memInactive = parseInt(mem[40]) / 1024;\nsetElementContent(\"mem_total\", memTotal.toFixed(1) + \" MB / \" + memSystem.toFixed(1) + \" MB\");\nsetElementContent(\"mem_free\", memFree.toFixed(1) + \" MB / \" + memTotal.toFixed(1) + \" MB\");\nsetElementContent(\"mem_used\", memUsed.toFixed(1) + \" MB / \" + memTotal.toFixed(1) + \" MB\");\nsetElementContent(\"mem_buffer\", memBuffer.toFixed(1) + \" MB / \" + memUsed.toFixed(1) + \" MB\");\nsetElementContent(\"mem_cached\", memCached.toFixed(1) + \" MB / \" + memUsed.toFixed(1) + \" MB\");\nsetElementContent(\"mem_active\", memActive.toFixed(1) + \" MB / \" + memUsed.toFixed(1) + \" MB\");\nsetElementContent(\"mem_inactive\", memInactive.toFixed(1) + \" MB / \" + memUsed.toFixed(1) + \" MB\");\n//\tsetElementContent(\"mem_hidden\", \"32 MB / 32 MB\");\n}\nvar update;\naddEvent(window, \"load\", function() {\nsetPacketInfo(\"SWRXgoodPacket=13663857;SWRXerrorPacket=0;SWTXgoodPacket=41650277;SWTXerrorPacket=0;\");\nsetMemoryValues(\",'total:','used:','free:','shared:','buffers:','cached:','Mem:','30326784','17903616','12423168','0','2170880','6172672','Swap:','0','0','0','MemTotal:','29616','kB','MemFree:','12132','kB','MemShared:','0','kB','Buffers:','2120','kB','Cached:','6028','kB','SwapCached:','0','kB','Active:','4948','kB','Inactive:','5008','kB','Active(anon):','1808','kB','Inactive(anon):','0','kB','Active(file):','3140','kB','Inactive(file):','5008','kB','Unevictable:','0','kB','Mlocked:','0','kB','SwapTotal:','0','kB','SwapFree:','0','kB','Dirty:','0','kB','Writeback:','0','kB','AnonPages:','1808','kB','Mapped:','1552','kB','Shmem:','0','kB','Slab:','4844','kB','SReclaimable:','848','kB','SUnreclaim:','3996','kB','KernelStack:','296','kB','PageTables:','204','kB','NFS_Unstable:','0','kB','Bounce:','0','kB','WritebackTmp:','0','kB','CommitLimit:','14808','kB','Committed_AS:','4692','kB','VmallocTotal:','1048304','kB','VmallocUsed:','20','kB','VmallocChunk:','1018108','kB'\");\n\tsetWirelessTable('xx:xx:xx:xx:41:AB','ath0','7:37:18','65M','6M','-36','-95','59','713','xx:xx:xx:xx:DE:19','ath0','0:42:51','65M','1M','-31','-95','64','775');\n\tsetWDSTable();\nsetDHCPTable( 'Redmi7A-Redmi','192.168.11.23','xx:xx:xx:xx:DE:19','1 day 00:00:00','23','DESKTOP-CBNBAUD','192.168.11.24','xx:xx:xx:xx:B6:BB','1 day 00:00:00','24','HUAWEI_P20_lite-2065a7d5e','192.168.11.52','xx:xx:xx:xx:41:AB','1 day 00:00:00','52');\nsetElementVisible(\"dhcp\", \"dhcp\" == \"dhcp\");\nupdate = new StatusUpdate(\"Info.live.htm\", 3);\nupdate.onUpdate(\"packet_info\", function(u) {\nsetPacketInfo(u.packet_info);\n});\nupdate.onUpdate(\"mem_info\", function(u) {\nsetMemoryValues(u.mem_info);\n});\nupdate.onUpdate(\"active_wireless\", function(u) {\neval('setWirelessTable(' + u.active_wireless + ')');\n});\nupdate.onUpdate(\"active_wds\", function(u) {\neval('setWDSTable(' + u.active_wds + ')');\n});\nupdate.onUpdate(\"dhcp_leases\", function(u) {\neval('setDHCPTable(' + u.dhcp_leases + ')');\n});\nupdate.onUpdate(\"lan_proto\", function(u) {\nsetElementVisible(\"dhcp\", u.lan_proto == \"dhcp\");\n});\nupdate.start();\n});\naddEvent(window, \"unload\", function() {\nupdate.stop();\n});\nfunction refresh(F)\n{F.submit();\n}\n//]]>\n</script>\n</head>\n<body class=\"gui\">\n\n<div id=\"wrapper\">\n<div id=\"content\" class=\"infopage\">\n<div id=\"header\">\n<div id=\"logo\"><h1>DD-WRT Control Panel</h1></div>\n<div id=\"menu\">\n<div id=\"menuMain\">\n<ul id=\"menuMainList\">\n<li><a href=\"index.asp\"><strong><script type=\"text/javascript\">Capture(bmenu.setup)</script></strong></a></li>\n\n<li><a href=\"Wireless_Basic.asp\"><strong><script type=\"text/javascript\">Capture(bmenu.wireless)</script></strong></a></li>\n\n<li><a href=\"Services.asp\"><strong><script type=\"text/javascript\">Capture(bmenu.services)</script></strong></a></li>\n<li><a href=\"Firewall.asp\"><strong><script type=\"text/javascript\">Capture(bmenu.security)</script></strong></a></li>\n<li><a href=\"Filters.asp\"><strong><script type=\"text/javascript\">Capture(bmenu.accrestriction)</script></strong></a></li>\n<li><a href=\"ForwardSpec.asp\"><strong><script type=\"text/javascript\">Capture(bmenu.applications)</script></strong></a></li>\n<li><a href=\"Management.asp\"><strong><script type=\"text/javascript\">Capture(bmenu.admin)</script></strong></a></li>\n<li><a href=\"Status_Router.asp\"><strong><script type=\"text/javascript\">Capture(bmenu.statu)</script></strong></a></li>\n</ul>\n</div>\n</div>\n</div>\n<div id=\"main\">\n<div id=\"contentsInfo\">\n<form name=\"Info\" action=\"apply.cgi\" method=\"post\">\n<input type=\"hidden\" name=\"submit_button\" value=\"Info\" />\n<input type=\"hidden\" name=\"next_page\" value=\"Info.htm\" />\n<input type=\"hidden\" name=\"change_action\" value=\"gozila_cgi\" />\n<input type=\"hidden\" name=\"submit_type\" value=\"refresh\" />\n\n<h2><script type=\"text/javascript\">Capture(info.h2)</script></h2>\n<div class=\"col2l\">\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(share.router)</script></legend>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(share.routername)</script></div>\nDD-WRT&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_router.sys_model)</script></div>\nTP-Link TL-WR1043ND&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\">LAN MAC</div>\n<script type=\"text/javascript\">\n//<![CDATA[\ndocument.write(\"<span id=\\\"lan_mac\\\" style=\\\"cursor:pointer; text-decoration:underline;\\\" title=\\\"\" + share.oui + \"\\\" onclick=\\\"getOUIFromMAC('F8:D1:11:BC:87:6E')\\\" >\");\ndocument.write(\"F8:D1:11:BC:87:6E\");\ndocument.write(\"</span>\");\n//]]>\n</script>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\">WAN MAC</div>\n<script type=\"text/javascript\">\n//<![CDATA[\ndocument.write(\"<span id=\\\"wan_mac\\\" style=\\\"cursor:pointer; text-decoration:underline;\\\" title=\\\"\" + share.oui + \"\\\" onclick=\\\"getOUIFromMAC('F8:D1:11:BC:87:6E')\\\" >\");\ndocument.write(\"F8:D1:11:BC:87:6E\");\ndocument.write(\"</span>\");\n//]]>\n</script>&nbsp;\n</div>\n\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(info.wlanmac)</script></div>\n<script type=\"text/javascript\">\n//<![CDATA[\ndocument.write(\"<span id=\\\"wl_mac\\\" style=\\\"cursor:pointer; text-decoration:underline;\\\" title=\\\"\" + share.oui + \"\\\" onclick=\\\"getOUIFromMAC('F8:D1:11:BC:87:6E')\\\" >\");\ndocument.write(\"F8:D1:11:BC:87:6E\");\ndocument.write(\"</span>\");\n//]]>\n</script>&nbsp;\n</div>\n\n<div class=\"setting\">\n<div class=\"label\">WAN IP</div>\n<span id=\"wan_ipaddr\">************</span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\">LAN IP</div>\n<span id=\"lan_ip\">************</span>&nbsp;\n</div>\n</fieldset><br />\n\n\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(share.wireless)</script></legend>\n\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(wl_basic.radio)</script></div>\n<span id=\"wl_radio\">Radio is On</span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(share.mode)</script></div>\n<script type=\"text/javascript\">Capture(wl_basic.ap)</script>&nbsp;\n&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_wireless.net)</script></div>\n<script type=\"text/javascript\">Capture(wl_basic.mixed)</script>&nbsp;\n&nbsp;\t\t\t\t\t\t\t\t\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(share.ssid)</script></div>\nseuhan_wifi&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(share.channel)</script></div>\n<span id=\"wl_channel\">2 (2417 MHz)</span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(wl_basic.TXpower)</script></div>\n<span id=\"wl_xmit\"><span style=\"display: none;\">20</span>20.0 dBm (EIRP av.)</span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(share.rates)</script></div>\n<span id=\"wl_rate\">144.444 Mb/s</span>&nbsp;\n</div>\n</fieldset><br />\n\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(status_wireless.legend2)</script></legend>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_wireless.rx)</script></div>\n<span id=\"packet_rx\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_wireless.tx)</script></div>\n<span id=\"packet_tx\"></span>&nbsp;\n</div>\n</fieldset><br />\n\n</div>\n\n<div class=\"col2r\">\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(info.srv)</script></legend>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(service.dhcp_legend2)</script></div>\n<script type=\"text/javascript\">Capture(share.enabled)</script>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\">WRT-radauth</div>\n<script type=\"text/javascript\">Capture(share.disabled)</script>&nbsp;\n</div>\n<!--\n<div class=\"setting\">\n<div class=\"label\">WRT-rflow</div>\n<script type=\"text/javascript\">Capture(share.disabled)</script>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\">MAC-upd</div>\n<script type=\"text/javascript\">Capture(share.disabled)</script>&nbsp;\n</div>\n-->\n\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(management.samba_legend)</script></div>\n<script type=\"text/javascript\">Capture(share.disabled)</script>&nbsp;\n</div>\n\n\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(bmenu.statuSputnik)</script></div>\n<script type=\"text/javascript\">Capture(share.disabled)</script>&nbsp;\n</div>\n\n\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(usb.usb_legend)</script></div>\n<script type=\"text/javascript\">Capture(share.disabled)</script>&nbsp;\n</div>\n\n</fieldset><br />\n\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(status_router.legend3)</script></legend>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_router.mem_tot)</script></div>\n<span id=\"mem_total\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_router.mem_free)</script></div>\n<span id=\"mem_free\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_router.mem_used)</script></div>\n<span id=\"mem_used\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_router.mem_buf)</script></div>\n<span id=\"mem_buffer\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_router.mem_cached)</script></div>\n<span id=\"mem_cached\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_router.mem_active)</script></div>\n<span id=\"mem_active\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_router.mem_inactive)</script></div>\n<span id=\"mem_inactive\"></span>&nbsp;\n</div>\n<!--\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_router.mem_hidden)</script></div>\n<span id=\"mem_hidden\"></span>&nbsp;\n</div>\n-->\n</fieldset><br />\n\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(status_router.legend6)</script></legend>\n<div class=\"setting\">\n<div class=\"label\">NVRAM</div>\n<span id=\"nvram\">25.86 KB / 64 KB</span>&nbsp;\n</div>\n\n<div class=\"setting\">\n<div class=\"label\">CIFS</div>\n<script type=\"text/javascript\">\n//<![CDATA[\nvar samba = {\n  \tfree: 0,\n  \tused: 0,\n  \tsize: 0\n  \t};\n\ndocument.write( ((0) && (samba.size)) ? (scaleSize(samba.used) + ' / ' + scaleSize(samba.size)) : '<span style=\"color:#999999;\"><em>(' + share.nmounted + ')</em></span>' );\n//]]>\n</script>\n</div>\n\n<!--\n<div class=\"setting\">\n<div class=\"label\">JFFS2</div>\n<script type=\"text/javascript\">\n//<![CDATA[\nvar my_jffs = {\n  \tfree: 0,\n  \tused: 6422528,\n  \tsize: 6422528\n  \t};\n\ndocument.write( ((0) && (my_jffs.size)) ? (scaleSize(my_jffs.used) + ' / ' + scaleSize(my_jffs.size)) : '<span style=\"color:#999999;\"><em>(' + share.nmounted + ')</em></span>' );\n//]]>\n</script>\n</div>\n-->\n<!--\n<div class=\"setting\">\n<div class=\"label\">MMC</div>\n<script type=\"text/javascript\">\n//<![CDATA[\nvar mmc = {\n  \tfree: 0,\n  \tused: 6422528,\n  \tsize: 6422528\n  \t};\n\ndocument.write( ((0) && (mmc.size)) ? (scaleSize(mmc.used) + ' / ' + scaleSize(mmc.size)) : '<span style=\"color:#999999;\"><em>(' + share.nmounted + ')</em></span>' );\n//]]>\n</script>\n</div>\n-->\n</fieldset><br />\n<!--\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(status_gpsi.legend)</script></legend>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_gpsi.status)</script></div>\n<span id=\"gps_text\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_gpsi.lon)</script></div>\n<span id=\"gps_lon\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_gpsi.lat)</script></div>\n<span id=\"gps_lat\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_gpsi.alt)</script></div>\n<span id=\"gps_alt\"></span>&nbsp;\n</div>\n<div class=\"setting\">\n<div class=\"label\"><script type=\"text/javascript\">Capture(status_gpsi.sat)</script></div>\n<span id=\"gps_sat\"></span>&nbsp;\n</div>\n</fieldset><br />\n-->\n\n</div><br clear=\"all\" />\n\n<h2><script type=\"text/javascript\">Capture(share.wireless)</script></h2>\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(status_wireless.legend3)</script></legend>\n<table class=\"table center\" cellspacing=\"5\" id=\"wireless_table\" summary=\"wireless clients table\">\n<tr>\n<th width=\"16%\"><script type=\"text/javascript\">Capture(share.mac)</script></th>\n<th width=\"10%\"><script type=\"text/javascript\">Capture(share.intrface)</script></th>\n<th width=\"10%\"><script type=\"text/javascript\">Capture(status_router.sys_up)</script></th>\n<th width=\"8%\"><script type=\"text/javascript\">Capture(share.txrate)</script></th>\n<th width=\"8%\"><script type=\"text/javascript\">Capture(share.rxrate)</script></th>\n<th width=\"8%\"><script type=\"text/javascript\">Capture(share.signal)</script></th>\n<th width=\"8%\"><script type=\"text/javascript\">Capture(share.noise)</script></th>\n<th width=\"8%\">SNR</th>\n<th width=\"24%\"><script type=\"text/javascript\">Capture(status_wireless.signal_qual)</script></th>\n</tr>\n</table>\n<script type=\"text/javascript\">\n//<![CDATA[\nvar t = new SortableTable(document.getElementById('wireless_table'), 1000);\n//]]>\n</script>\n</fieldset><br />\n\n<div id=\"wds\" style=\"display:none\">\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(status_wireless.wds)</script></legend>\n<table class=\"table center\" cellspacing=\"5\" id=\"wds_table\" summary=\"wds clients table\">\n<tr>\n<th width=\"16%\"><script type=\"text/javascript\">Capture(share.mac)</script></th>\n<th width=\"10%\"><script type=\"text/javascript\">Capture(share.intrface)</script></th>\n<th width=\"26%\"><script type=\"text/javascript\">Capture(share.descr)</script></th>\n<th width=\"8%\"><script type=\"text/javascript\">Capture(share.signal)</script></th>\n<th width=\"8%\"><script type=\"text/javascript\">Capture(share.noise)</script></th>\n<th width=\"8%\">SNR</th>\n<th width=\"24%\"><script type=\"text/javascript\">Capture(status_wireless.signal_qual)</script></th>\n</tr>\n</table>\n<script type=\"text/javascript\">\n//<![CDATA[\nvar t = new SortableTable(document.getElementById('wds_table'), 1000);\n//]]>\n</script>\n</fieldset><br />\n\n</div>\n\n\n<div id=\"dhcp\" style=\"display:none\">\n<h2><script type=\"text/javascript\">Capture(share.dhcp)</script></h2>\n<fieldset>\n<legend><script type=\"text/javascript\">Capture(status_lan.legend3)</script></legend>\n<table class=\"table center\" cellspacing=\"5\" id=\"dhcp_leases_table\" summary=\"dhcp leases table\">\n<tr>\n<th width=\"25%\"><script type=\"text/javascript\">Capture(share.hostname)</script></th>\n<th width=\"25%\"><script type=\"text/javascript\">Capture(share.ip)</script></th>\n<th width=\"25%\"><script type=\"text/javascript\">Capture(share.mac)</script></th>\n<th width=\"25%\"><script type=\"text/javascript\">Capture(idx.dhcp_lease)</script></th>\n</tr>\n</table>\n<script type=\"text/javascript\">\n//<![CDATA[\nvar t = new SortableTable(document.getElementById('dhcp_leases_table'), 1000);\n//]]>\n</script>\n</fieldset><br />\n</div>\n\n<div class=\"submitFooter\">\n<script type=\"text/javascript\">\n//<![CDATA[\nvar autoref = sbutton.autorefresh;\nsubmitFooterButton(0,0,0,autoref);\n//]]>\n</script>\n</div>\n</form>\n<div class=\"center\">\n<a href=\"http://www.dd-wrt.com/\">DD-WRT</a><br /><form action=\"https://www.paypal.com/cgi-bin/webscr\" method=\"post\" target=\"_blank\"><input type=\"hidden\" name=\"cmd\" value=\"_xclick\" /><input type=\"hidden\" name=\"business\" value=\"<EMAIL>\" /><input type=\"hidden\" name=\"item_name\" value=\"DD-WRT Development Support\" /><input type=\"hidden\" name=\"no_note\" value=\"1\" /><input type=\"hidden\" name=\"currency_code\" value=\"EUR\" /><input type=\"hidden\" name=\"lc\" value=\"en\" /><input type=\"hidden\" name=\"tax\" value=\"0\" /><input type=\"image\" src=\"images/paypal.gif\" name=\"submit\" /></form>\n</div><br />\n</div>\n</div>\n<div id=\"floatKiller\"></div>\n<div id=\"statusInfo\">\n<div class=\"info\"><script type=\"text/javascript\">Capture(share.firmware)</script>:\r\n<script type=\"text/javascript\">\n//<![CDATA[\ndocument.write(\"<a title=\\\"\" + share.about + \"\\\" href=\\\"javascript:openAboutWindow()\\\">DD-WRT v24SP2- (03/25/13) std</a>\");\n//]]>\n</script>\n</div>\n<div class=\"info\"><script type=\"text/javascript\">Capture(share.time)</script>:  <span id=\"uptime\"> 02:29:12 up 9 days, 11:41,  load average: 0.00, 0.01, 0.04</span></div>\n<div class=\"info\">WAN<span id=\"ipinfo\">&nbsp;IP: ************</span></div>\n</div>\n</div>\n</div>\n</body>\n</html>\n",
  "nhash": "5071545739019781781",
  "ehash": "f2ef7eefddda76efa002dcc558c3b393",
  "lastupdatetime": "2020-11-22 09:29:29"
}`
	obj, err := structs.NewJsonObj(d)
	assert.Nil(t, err)
	ps, err := Products(obj)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(ps))

	//getProductManager().setDebug(true)
	ps, err = Products(obj)
	assert.Nil(t, err)
	assert.Equal(t, 1, len(ps))

	// 全加载测试
	f := filepath.Join(os.Getenv("GOPATH"), "src", "git.gobies.org", "sutra", "gosutra", "resources", "rules.json")
	if fi, err := os.Stat(f); err == nil && fi.Size() > 0 {
		_, err := LoadFromFile(context.Background(), f, true)
		assert.Nil(t, err)
		ps, err = Products(obj)
		assert.Nil(t, err)
		assert.True(t, len(ps) > 1)
	}
}

func BenchmarkProducts(b *testing.B) {
	d := `{
  "host": "***************:8000",
  "ip": "***************",
  "port": "8000",
  "header": "HTTP/1.1 200 OK\r\nConnection: close\r\nContent-Length: 25930\r\nAccept-Ranges: bytes\r\nContent-Type: text/html; charset=utf-8\r\nDate: Wed, 12 Aug 2020 01:13:09 GMT\r\nLast-Modified: Tue, 28 Jul 2020 22:46:46 GMT\r\n",
  "body": "<!doctype html><html lang=\"en\"><head><meta charset=\"utf-8\"><meta name=\"viewport\" content=\"width=device-width,initial-scale=1\"><link rel=\"shortcut icon\" href=\"/favicon.ico\"><link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/favicon-32x32.png\"><link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/favicon-16x16.png\"><link rel=\"stylesheet\" href=\"/3rdpartystatic/cdnjs.cloudflare.com/ajax/libs/codemirror/5.25.2/theme/neo.css\"/><title>Dgraph Ratel Dashboard</title><script>function isTemplateAnnotation(n){return 0===n.indexOf(\")\\x26\\x26n.lastIndexOf(\")===n.length-2}function getServerAddr(n){return isTemplateAnnotation(n)?\"\":n}window.SERVER_ADDR=getServerAddr(\"\")</script><script>\n      window.RATEL_LOADER_HTML = \"\";\n      window.RATEL_LOADER_HTML = \"<style>\\n  #cachedjs-root {\\n    display: none;\\n  }\\n</style>\\n\\n  <h1 class=\\\"caption\\\">\\n    Choose a version of the Ratel interface\\n  </h1>\\n  <div class=\\\"cards\\\">\\n    <div class=\\\"card\\\">\\n      <div class=\\\"card-body\\\">\\n        <h5 class=\\\"card-title\\\">Dev</h5>\\n        <h6 class=\\\"card-subtitle mb-2 text-muted\\\">\\n          <i class=\\\"icon-warning\\\"></i> Bleeding Edge. Unstable\\n        </h6>\\n\\n        <p class=\\\"card-text\\\">\\n          Includes latest unreleased improvements, features, experiments, sometimes bugs. Feeback is welcome in\\n          <a href='https://discuss.dgraph.io' target=_blank>Discuss</a>\\n        </p>\\n        <a href='/?dev' class='card-link'>\\n          <i class='icon-launch'></i> Launch Dev\\n        </a>\\n      </div>\\n    </div>\\n\\n    <div class='card recommended'>\\n      <div class='card-body'>\\n        <h5 class='card-title'><i class='icon-recommended'></i> Latest</h5>\\n        <h6 class='card-subtitle mb-2 text-muted'>\\n          Official Stable Release\\n        </h6>\\n\\n        <p class='card-text'>Approved for everyday use. This version is updated more often than the Dgraph server (usually once a week or faster). It contains finalized features, bugfixes, improvements, etc.</p>\\n        <a href='/?latest' class='card-link'>\\n          <i class='icon-launch'></i> Launch Latest\\n        </a>\\n      </div>\\n    </div>\\n\\n\\n    <div class='card'>\\n      <div class='card-body'>\\n        <h5 class='card-title'>Local Bundle</h5>\\n        <h6 class='card-subtitle mb-2 text-muted'>\\n          Works Offline. Never auto-updates\\n        </h6>\\n\\n        <p class='card-text'>This version of the UI was compiled into your ratel binary. It doesn't require internet connection to run, but will never get updated unless you install a new version of Ratel.</p>\\n        <a href='/?local' class='card-link'>\\n          <i class='icon-launch'></i> Launch Offline\\n        </a>\\n      </div>\\n    </div>\\n</div>\\n\\n<div class='form-check'>\\n  <input type='checkbox' value='' id='cookieCheckbox'>\\n  <label for='cookieCheckbox'>\\n    Always launch <em>Latest</em> and never ask again\\n  </label>\\n</div>\\n\\n<link href='https://fonts.googleapis.com/css?family=Raleway' rel='stylesheet'>\\n\";\n      </script><script>window.RATEL_CDN_MODE=\"prod\",window.RATEL_CDN_MAP={cachedjs:\"\",dev:\"https://d1ocqy7wcgv7nr.cloudfront.net/dev/\",latest:\"https://d1ocqy7wcgv7nr.cloudfront.net/\",local:\"\"}</script></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id=\"root\"><style>body,html{height:100vh;width:100vw;overflow:hidden;margin:0;background-color:#f8f9fa}.text-muted{color:#6c757d!important}*,::after,::before{box-sizing:border-box}html{font-family:sans-serif;line-height:1.15}body{margin:0;font-family:-apple-system,BlinkMacSystemFont,\"Segoe UI\",Roboto,\"Helvetica Neue\",Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",\"Noto Color Emoji\";font-size:1rem;font-weight:400;line-height:1.5;color:#212529;text-align:left}hr{box-sizing:content-box;height:0;overflow:visible}h1,h2,h3,h4,h5,h6{margin-top:0;margin-bottom:.5rem}p{margin-top:0;margin-bottom:1rem}a{color:#007bff;text-decoration:none;background-color:transparent;-webkit-text-decoration-skip:objects}a:hover{color:#0056b3;text-decoration:underline}.h1,.h2,.h3,.h4,.h5,.h6,h1,h2,h3,h4,h5,h6{margin-bottom:.5rem;font-family:inherit;font-weight:500;line-height:1.2;color:inherit}.h1,h1{font-size:2.5rem}.h2,h2{font-size:2rem}.h3,h3{font-size:1.75rem}.h4,h4{font-size:1.5rem}.h5,h5{font-size:1.25rem}.h6,h6{font-size:1rem}.card{position:relative;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-width:0;word-wrap:break-word;background-color:#fff;background-clip:border-box;border:1px solid rgba(0,0,0,.125);border-radius:.25rem}.card>hr{margin-right:0;margin-left:0}.card>.list-group:first-child .list-group-item:first-child{border-top-left-radius:.25rem;border-top-right-radius:.25rem}.card>.list-group:last-child .list-group-item:last-child{border-bottom-right-radius:.25rem;border-bottom-left-radius:.25rem}.card-body{-ms-flex:1 1 auto;flex:1 1 auto;padding:1.25rem}.card-title{margin-bottom:.75rem}.card-subtitle{margin-top:-.375rem;margin-bottom:0}.card-text:last-child{margin-bottom:0}.card-link:hover{text-decoration:none}.card-link+.card-link{margin-left:1.25rem}.card-header{padding:.75rem 1.25rem;margin-bottom:0;background-color:rgba(0,0,0,.03);border-bottom:1px solid rgba(0,0,0,.125)}.card-header:first-child{border-radius:calc(.25rem - 1px) calc(.25rem - 1px) 0 0}.card-header+.list-group .list-group-item:first-child{border-top:0}.card-footer{padding:.75rem 1.25rem;background-color:rgba(0,0,0,.03);border-top:1px solid rgba(0,0,0,.125)}.card-footer:last-child{border-radius:0 0 calc(.25rem - 1px) calc(.25rem - 1px)}.card-header-tabs{margin-right:-.625rem;margin-bottom:-.75rem;margin-left:-.625rem;border-bottom:0}.card-header-pills{margin-right:-.625rem;margin-left:-.625rem}.card-img-overlay{position:absolute;top:0;right:0;bottom:0;left:0;padding:1.25rem}.card-img{width:100%;border-radius:calc(.25rem - 1px)}.card-img-top{width:100%;border-top-left-radius:calc(.25rem - 1px);border-top-right-radius:calc(.25rem - 1px)}.card-img-bottom{width:100%;border-bottom-right-radius:calc(.25rem - 1px);border-bottom-left-radius:calc(.25rem - 1px)}@font-face{font-family:fontello;src:url(data:application/octet-stream;base64,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) format('woff'),url(data:application/octet-stream;base64,AAEAAAAPAIAAAwBwR1NVQiCLJXoAAAD8AAAAVE9TLzI+L1JGAAABUAAAAFZjbWFwt4m2fQAAAagAAAHOY3Z0IAbV/uYAABAQAAAAIGZwZ22KkZBZAAAQMAAAC3BnYXNwAAAAEAAAEAgAAAAIZ2x5ZtYZUwYAAAN4AAAIrmhlYWQTUMpWAAAMKAAAADZoaGVhB1YDWAAADGAAAAAkaG10eBiN/+IAAAyEAAAAHGxvY2EHkAqhAAAMoAAAABBtYXhwAZcMugAADLAAAAAgbmFtZcydHiAAAAzQAAACzXBvc3Rfk4xGAAAPoAAAAGhwcmVw5UErvAAAG6AAAACGAAEAAAAKADAAPgACREZMVAAObGF0bgAaAAQAAAAAAAAAAQAAAAQAAAAAAAAAAQAAAAFsaWdhAAgAAAABAAAAAQAEAAQAAAABAAgAAQAGAAAAAQAAAAEDggGQAAUAAAJ6ArwAAACMAnoCvAAAAeAAMQECAAACAAUDAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFBmRWQAQOgA8TUDUv9qAFoDUgClAAAAAQAAAAAAAAAAAAUAAAADAAAALAAAAAQAAAF2AAEAAAAAAHAAAwABAAAALAADAAoAAAF2AAQARAAAAAoACAACAALoAug08JbxNf//AADoAOg08JbxNf//AAAAAAAAAAAAAQAKAA4ADgAOAAAAAQACAAMABAAFAAYAAAEGAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAwAAAAAAFgAAAAAAAAABgAA6AAAAOgAAAAAAQAA6AEAAOgBAAAAAgAA6AIAAOgCAAAAAwAA6DQAAOg0AAAABAAA8JYAAPCWAAAABQAA8TUAAPE1AAAABgAAAAT/4/+WBB4DJgAMABkAHgApAExASSIBBAYBRwAGAAQABgRtCAECBwEABgIAYAAEAAUBBAVgAAEDAwFUAAEBA1gAAwEDTA4NAQAoJx4dHBsVEg0ZDhkIBQAMAQwJBRQrASIHAQYWMyEyNicBJicyFwEWBiMhIiY3ATYTNDIUIhMUDwEnJjU0PgEWAgIxIP7MICpCAnFBLCL+zSEvaj8BND9nff2Pe2tAATU+J4iIkgZHSQYuQiwCvTf9/zdQUDcCATdpa/3/abu5awIBa/10RYgBfA4Ps7MPDiAuAjIAAAAABAAA/6ECaAMbACYA5wD0AQEBeEAyurSyAxYOx6YCDRbWnAIMDXg+AgQDakkCFQRgVwIFFQkBBgUbEgIABwhHCAYFBAIFAURLsAxQWEBmEQEPEA4QDw5tEgEOFhAOFmsYEwINFgwMDWUaFwoDBAMVAwRlCQEFFQYVBQZtCAEGBxUGB2sCAQAHAQcAAW0AAQFuABAAFg0QFmAUAQwLAQMEDANhGQEVBQcVVBkBFRUHWAAHFQdMG0BoEQEPEA4QDw5tEgEOFhAOFmsYEwINFgwWDQxtGhcKAwQDFQMEFW0JAQUVBhUFBm0IAQYHFQYHawIBAAcBBwABbQABAW4AEAAWDRAWYBQBDAsBAwQMA2EZARUFBxVUGQEVFQdYAAcVB0xZQTQA9gD1AOkA6AD8APsA9QEBAPYBAQDvAO4A6AD0AOkA9ADnAOUA3QDbAM4AzADAAL4AtwC2AK8ArgCjAKIAmACXAJEAjwCKAIcAfwB9AHEAbwAmABkAGwAbABsALQAlACUALwAbAAUAHSslMxcPAScHLwE3Fh8BFhcWMzI3FxYXFjMyNzY/ARYzMjc2PwE2NxYTFhUUBwYnIzEXFhceAQYHBgcGIyInJi8BFxYHBgcGIyIvARUWFxYHBgcGIyInJi8BFRYHBiInJjc1BwYjIiYnJjc2PwEHBgcGIyInJicmNzY/AQcGBwYjIicmNzQ3Nj8BMSMGJyY0NzYXMycmJyY3NjMyFxYfAScmNjc2MzIfAScmJyY3Njc2MhcWHwE1JjYyFgcVNzY3NjMyFxYXFgcGBxU3Njc2MzIXFhcWBwYPAjc2NzYzMhcWFxYHBg8BMzYFMj4BNC4BIg4BFB4BNyIuATQ+ATIeARQOAQHwA3V9TmhoVHl3AQgBCh0SExYLBAoGFRoYFgcJAg8UEBQbDAMHAgJnCAgFTgkRHA0XAgMEBw0KCgQeGAcECDoEBA0SEwYtDAsIDwUKEA0MCA0IDQgCCwooCwsCAiEQCh4KBg4JCgMFAw4mBRISDgMCGxIPBAUGGR0DGw8IAxgNHBEHTwYICAZOCwtJAQMIDhwDHRkGBQk4Bg4REwg0BQMKCQ4GCBENFxMMCAECFSkVAgUOCA4JDgsSCAUPCAsCCBYaBRQRDAUDEwoXCwIEBxgeBAsJDQcJBAFICw1N/uI0WTQ0WWlaNTVaNSlGKChGUkQnJ0Tn3wtcpKReCeEJDQEODwcHAwkDDAwDCQMHBw0QBQsHAgEnCxQRDQwCBwsGDA0PCxIJBxENBQIINQsODRMyDQQVFiUGDgUGGA0dEAhOBQkJBU4LBVALDgYlFxQEAgIRKhMPDAgdFA0CAgQOESIUDQYMBgsHAgwJLAgKAgQcDQ0SIREOBAIJNRgQEDsEBRUWJQgJCAUkGBYDDE4ODk4JDB0PGgUHCgglFBcFBAsWGhAODgcUDBQLBAIFDREGCBMTDA0cBALrNVlpWjQ0WmlZNS0oRVFFKChFUUUoAAAAAgAA//kDoAMLAC0AQgBOQEs7AQQGJQEFBAJHAAcBAgEHAm0ABgIEAgYEbQAEBQIEBWsABQMCBQNrAAEAAgYBAmAAAwAAA1QAAwMAWAAAAwBMFBcVJzU5NTMIBRwrARUUBiMhIiY1ETQ2NyEyFx4BDwEGIycmIyEiBgcRFBYXITI2PQE0PwE2MzIXFhMBBiIvASY0PwE2Mh8BATYyHwEWFAMSXkP+MENeXkMB0CMeCQMHGwYHBQ0M/jAlNAE2JAHQJTQFJAYHAwQLgf45DSQO8A4OPQ4kDpMBaQ0kDj4NAUuxQ15eQwHQQl4BDgQTBhwFAQM0Jf4wJTQBNiSNCAUjBgIEAQX+Og4O8A0kDj4NDZMBaQ0NPQ4kAAL///9bA+oDUgAfAEEALUAqBAECAAFHMQEBRAACAAEAAgFtAAEBbgMBAAAMAEkBACEgFBMAHwEfBAUUKwEiBwYHMTY3NhcWFxYXFgYHBhceATc+ATc2JicuAScmASIHBgcGBwYWFxYXFhcWNzY3MQYHBicmJyYnJjY3NiYnJgHyV1FURFZsamdqT0IhIQYlDhoQMxEDCgIjASUmkF5b/gUYDwQEBgEkAiQmSFt7d3l9YVZsamdrT0IhIAUlCAYOEgNSHR45RRUUHiBPQlZTs1EpGxABEQMPBlrDWV2QJiX+7hAEBggGWsNZXUhbJCIYGVFFFRQeIE9CVlOzURUhDhIAAAAAAgAA//kDEwMLAA8AHwArQCgAAwQBAAEDAGAAAQICAVQAAQECWAACAQJMAgAeGxYTCgcADwIPBQUUKwEhIgYHERQWFyEyNjURNCYXERQGIyEiJjURNDY3ITIWAnH+MCU0ATYkAdAlNDR8XkP+MENeXkMB0EJgAsM0Jf4wJTQBNiQB0CU0Wf4wQ15eQwHQQl4BYAAAAAIAAP98A6EDCwAIAC0AL0AsGhkPDgQCRAABAAIAAQJtAAICbgADAAADVAADAwBYAAADAEwrKiYlExIEBRYrATQmIgYUFjI2NxQGBwYPAQYPAQYiLwEmPwEnBwYjIi8BJj8BNj8BNjc+ATMyFgMkICwgICwgfVZiLUALAQjWBAwGJAcDL5yaAgQHBSQKB30GCdM2LGm+kgcMAlgWICAsICC3i8plLDbTCQZ9AgUkBwqbnC8BBSQLC9YIAQtALWhQDAAAAAABAAAAAQAAJV8AH18PPPUACwPoAAAAANgYQ2IAAAAA2BhDYv/j/*******************************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) format('truetype')}[class*=\" icon-\"]:before,[class^=icon-]:before{font-family:fontello;font-style:normal;font-weight:400;speak:none;display:inline-block;text-decoration:inherit;width:1em;margin-right:.2em;text-align:center;font-variant:normal;text-transform:none;line-height:1em;margin-left:.2em;text-shadow:1px 1px 1px rgba(127,127,127,.3)}.icon-warning:before{content:'\\e800'}.icon-recommended:before{content:'\\e801'}.icon-check:before{content:'\\e802'}.icon-spinner:before{content:'\\e834'}.icon-check-empty:before{content:'\\f096'}.icon-launch:before{content:'\\f135'}.icon-warning{color:#ffc107;margin-left:-4px}.icon-recommended{color:#28a745}.spinner{animation:spin 2s infinite linear;display:block;margin-bottom:10px;font-size:48px}@keyframes spin{from{transform:rotate(0)}to{transform:rotate(359deg)}}#cachedjs-offline-message p{font-size:18px}#cachedjs-offline-message h4{margin:30px 0 40px}#cachedjs-offline-message .icon-warning{font-size:48px;color:#82211b;text-align:center;display:block;float:left;margin-right:16px;margin-left:-10px}.icon-recommended{position:absolute;top:8px;right:6px}.mb-2{margin-bottom:.5rem!important}.cards{display:flex;flex-direction:row;flex-wrap:wrap;justify-content:space-around;max-width:1100px;margin:0 auto}.card{display:block;float:left;width:320px;margin:20px 0;position:relative}.card.recommended{box-shadow:#007bff 0 0 20px -4px}.card p{min-height:120px}@media (max-width:970px){.card{width:300px}}@media (max-width:930px){body,html{overflow-y:auto;height:auto}}.form-check,h1.caption{text-align:center;font-family:Raleway,sans-serif;margin:40px 0 20px}.form-check{margin-top:20px}em{font-style:normal;border-bottom:1px dashed #666}</style><div id=\"cachedjs-root\" style=\"width:400px;margin:10vh auto;text-align:center\"><div id=\"cachedjs-loading-text\" style=\"margin:20px auto;font-size:24px;display:none\"><i class=\"icon-spinner spinner\" aria-hidden=\"true\"></i> Loading...<br/><br/><a href=\"/?nocookie\" style=\"font-size:16px\">Go to the release selection screen</a></div><div id=\"cachedjs-offline-message\" style=\"margin:20px auto;text-align:justify;display:none\"><h4><i class=\"icon-warning\" aria-hidden=\"true\"></i> We seem to have difficulties loading Ratel client</h4><p>Try using the <a id=\"cachedjs-link\" href=\"#cachedjs\">offline version</a> or <a href=\"/?nocookie\">choose another release</a>.</p><p>(Or continue waiting if you have a slow internet connection)</p></div></div></div><script>function injectJs(e){var t=document.createElement(\"script\");t.type=\"text/javascript\",t.src=e,document.body.appendChild(t)}function injectUrls(e,t){if(\"dev\"!==window.RATEL_CDN_MODE){injectJs(e);var o=document.createElement(\"link\");o.type=\"text/css\",o.rel=\"stylesheet\",o.href=t,document.head.appendChild(o)}else console.info(\"Dev mode detected. Not injecting URLs.\")}function loadPrefixedJS(e){console.info(\"Loading Ratel code from \",e),injectUrls(e+\"static/js/main.js\",e+\"static/css/main.css\")}function setVisibility(e,t){try{document.getElementById(e).style.display=t?null:\"none\"}catch(o){console.error(\"Unable to set visibility of \"+e+\"  to \"+t)}}function startLoadingTimer(e){setTimeout((function(){document.getElementById(\"cachedjs-root\")&&(setVisibility(\"cachedjs-loading-text\",!1),setVisibility(\"cachedjs-offline-message\",!0),document.getElementById(\"cachedjs-link\").href=window.location.origin+\"?local\")}),2e4)}window.RATEL_AUTOLOAD_URL_STORAGE_KEY=\"ratelAutoloadUrl\",setVisibility(\"cachedjs-loading-text\",!0);var storedCdn=localStorage.getItem(RATEL_AUTOLOAD_URL_STORAGE_KEY),urlParams=new URLSearchParams(window.location.search),paramsArray=Array.from(urlParams.entries()),codeVersion=paramsArray.length?paramsArray[0][0]:null;window.RATEL_CDN_MAP.hasOwnProperty(codeVersion)||(codeVersion=urlParams.get(\"cdn\")),window.RATEL_CDN_MAP.hasOwnProperty(codeVersion)?(\"dev\"===codeVersion&&(window.RATEL_DEV_MODE=!0),loadPrefixedJS(window.RATEL_CDN_MAP[codeVersion]),startLoadingTimer()):storedCdn&&\"\"!==urlParams.get(\"nocookie\")?(loadPrefixedJS(window.RATEL_CDN_MAP[storedCdn]||storedCdn),startLoadingTimer()):window.RATEL_LOADER_HTML&&(document.getElementById(\"root\").innerHTML+=window.RATEL_LOADER_HTML,injectJs(\"/loader.js\"))</script></body></html>",
  "nhash": "427729767338876983",
  "ehash": "f9fb104fdc96b22e73c5269d63d6abbb",
  "lastupdatetime": "2020-08-12 09:12:47"
}
`
	log.Println(len(d))
	d1 := `{
  "host": "*************",
  "ip": "*************",
  "port": "80",
  "header": "HTTP/1.1 200 OK\r\nConnection: close\r\nTransfer-Encoding: chunked\r\nContent-Type: text/html\r\nDate: Thu, 03 May 2018 07:50:41 GMT\r\nServer: Seminole/2.71 (Linux; DF7)\r\n",
  "body": "<!DOCTYPE html>\n<html>\n<head>\n\t<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n\n\t<title>AJA HELO</title>\n\n\t<link rel=\"icon\" href=\"favicon.ico\" type=\"image/x-icon\"/>\n\t<link rel=\"shortcut icon\" href=\"favicon.ico\" type=\"image/x-icon\"/>\n\t\n\t<link href=\"/css/smoothness/jquery-ui-1.11.4.custom.min.css \" rel=\"stylesheet\" type=\"text/css\" id=\"theme\"/>\n\t<link href=\"/css/select2.css\" rel=\"stylesheet\" type=\"text/css\" />\n\t<link href=\"/css/kipro.css\" rel=\"stylesheet\" type=\"text/css\" />\n\t<link href=\"/css/gang.css\" rel=\"stylesheet\" type=\"text/css\" />\n\t<link href=\"/css/jquery.fileupload-ui.css\" rel=\"stylesheet\" type=\"text/css\"/>\n\t<link href=\"/css/upload_toolbar.css\" rel=\"stylesheet\" type=\"text/css\"/>\n\t<link href=\"/css/mini_player.css\" rel=\"stylesheet\" type=\"text/css\"/>\n\n\n\t<script src=\"../scheduler/dhtmlxscheduler.js\" type=\"text/javascript\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_serialize.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_recurring.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_readonly.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_multisource.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_active_links.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_container_autoresize.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_tooltip.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_minical.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_key_nav.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_agenda_view.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<script src=\"../scheduler/ext/dhtmlxscheduler_limit.js\" type=\"text/javascript\" charset=\"utf-8\"></script>\n\t<link rel=\"stylesheet\" href=\"../scheduler/dhtmlxscheduler.css\" type=\"text/css\" />\n\t<link rel=\"stylesheet\" href=\"/css/scheduler.css\" type=\"text/css\" />\n\t<link rel=\"stylesheet\" href=\"/css/jquery.clockpicker.css\" type=\"text/css\" />\n\n\n\n\n\n\t<script type=\"text/javascript\" src=\"js/kipro.common.js\"></script>\n\n\n\t<script type=\"text/javascript\">\n\t\tsetRelease(true);\n\t</script>\n\n\n\t<script type=\"text/javascript\">\n\t/*\n * AJA: BG-commented out code that is not used prior to deleting input_control.js\n * Made changes for window.resize because it was throwing exceptions-marked with: //BG\n */\n\nvar monitor = null;\n\njQuery(document).ready(function() {\n\n\tjQuery(document).keydown(function(event) {\n\t\tif (event.keyCode == '27') {\n\t\t\tevent.preventDefault();\n\t\t}\n\t});\n\n\ttry {\n\t\t// determine if we are coming to this page from the firmware page\n\t\t// if so, we need to show the correct page from the url\n\t\tvar hash = window.location.hash;\n\t\tif (hash.length > 1)\n\t\t{\n\t\t\t// we have a page name in our url...\n\t\t\tvar pageName = hash.substr(1); // skip the hash\n\t\t\tif (pageName) {\n\t\t\t\tnav_page(pageName);\n\t\t\t}\n\t\t}\n\t} catch (e) { debug(\"Caught exception: \" + e); }\n\n\tvar options = {\n\t\t\tfxName: \"slide\",\n\t\t\tfxSpeed: \"slow\",\n\t\t\tinitClosed: false,\n\t\t\tslidable: false,\n\t\t\tclosable: false,\n\t\t\tresizable: false,\n\t\t\tautoResize: true,\n\t\t\tautoReopen: true,\n\t\t\tshowErrorMessages:false, //off for production - set true if debugging\n\t\t\tcenter__paneSelector: \".outer-layout-center.ui-layout-center\",\n\t\t\tnorth__paneSelector: \".outer-layout-north.ui-layout-north\",\n\t\t\tnorth__size: 80,\n\t\t\tcenter__minHeight: 200,\n\t\t\tcenter__minWidth:400,\n\t\t\tcenter__onresize: function() {\n\t\t\t\t// We do not want it to auto-show on any resize event, especially during firmware update.\n\t\t\t\t//if (monitor.state.north.isHidden && kipro_default.state.container.innerHeight > 260)\n\t\t\t\t//\tmonitor.show('north');\n\t\t\t},\n\t\t\teast__size: 200,\n\t\t\twest__size: 200,\n\t\t\tscrollToBookmarkOnLoad: false,\n\t\t\tspacing_open: 0,\n\t\t\toverflow: \"hidden\",\n\t\t\tapplyDefaultStyles: false\n\t};\n\n\tvar monitor_options = {\n\t\t\tfxName: \"slide\",\n\t\t\tfxSpeed: \"slow\",\n\t\t\tinitClosed: false,\n\t\t\tclosable: false,\n\t\t\tresizable: false,\n\t\t\tshowErrorMessages:false, //off for production - set true if debugging\n\t\t\t// north__paneSelector: \"middle-north\",\n\t\t\t// south__contentSelector: \"middle-south\",\n\t\t\tnorth__paneSelector: \".middle-north.ui-layout-north\",\n\t\t\tcenter__paneSelector: \".middle-center.ui-layout-center\",\n\t\t\tnorth__minHeight: 100,\n\t\t\tcenter__minHeight: 100,\n\t\t\tnorth__onhide_start: function() {\n\t\t\t\t//alert(\"hide start\");\n\t\t\t\tif ($(document.getElementById(\"transport_controls_frame\"))){\n\t\t\t\t\tjQuery(document.getElementById(\"transport_controls_frame\")).css(\"display\", \"none\");\n\t\t\t\t  }\n\t\t\t\t},\n\t\t\tnorth__onopen_start: function() {\n\t\t\t\tif ($(document.getElementById(\"transport_controls_frame\"))){\n\t\t\t\t\tjQuery(document.getElementById(\"transport_controls_frame\")).css(\"display\", \"inline\");\n\t\t\t\t}\n\t\t\t},\n\t\t\teast__size: 0,\n\t\t\twest__size: 0,\n\t\t\tsouth__size: 0,\n\t\t\tscrollToBookmarkOnLoad: false,\n\t\t\tspacing_open: 0,\n\t\t\toverflow: \"auto\",\n\t\t\tapplyDefaultStyles: false\n\t};\n\n\tkipro_default = jQuery(\"body\").layout(options);\n\ttry {\n\t\tmonitor = jQuery(\".monitor\").layout(monitor_options);\n\t} catch (e) {debug(e);}\n\n\tjQuery(\".static_text\").each( function (index, element) {\n\t\tvar paramid = jQuery.paramid(element);\n\t\tvar url = \"/config?action=get&paramid=\" + paramid + \"&configid=0&alt=json\";\n\t\tjQuery.ajax({\n\t\t\t\turl: url,\n\t\t\t        dataType:\"json\",\n\t\t\t    \tsuccess: function (data,resultText,xhr) {\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t//var data = new Function('return '+data)();\n\t\t\t\t\t\t\t//var data = jQuery.parseJSON(data);\n\t\t\t\t\t\t\tif (paramid == \"eParamID_SWVersion\") {\n\t\t\t\t\t\t\t\tvar value = +data.value;\n\t\t\t\t\t\t\t\tvar oct1 = (value >> 24) & 0xFF;\n\t\t\t\t\t\t\t\tvar oct2 = (value >> 16) & 0xFF;\n\t\t\t\t\t\t\t\tvar oct3 = (value >> 8) & 0xFF;\n\t\t\t\t\t\t\t\tvar oct4 = (value >> 0) & 0xFF;\n\t\t\t\t\t\t\t\tvar valueStr = oct1 + \".\" + oct2 + \".\" + oct3 + \".\" + oct4;\n\t\t\t\t\t\t\t\tjQuery(element).text(valueStr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tjQuery(element).text(data.value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (e) { debug(e.name + \", \" + e.message);\t}\n\t\t\t\t}\n\t\t}); // ajax\n\t}); // each\n\n\tjQuery(\".window_header_button\").click( function (event) {\n\t\tvar window = jQuery(this).parents(\".aja_window\");\n\t\tjQuery(window).find(\".window_content\").toggle();\n\t\tjQuery(window).toggleClass(\"closed\");\n\t});\n\n\tjQuery(\".alarm_icon\").click( function(event) {\n\t\tif ( jQuery(\".main_content.alarm_config_page\").hasClass(\"loading\") ) {\n\t\t\tcreatePage(\"alarm_config_page\");\n\t\t}\n\t\tjQuery(\"#alarm_config_page\").dialog({ title:'Alarm Configuration', modal:true, width: 600, position:'center', dialogClass: 'dialog_config', resizable:false });\n\t});\n\n\tjQuery(\".event_network_window\").click( function(event) {\n\t\tvar event = $(document).fire('aja:pre_page_navigate', {\n\t\t\tcurrent_page: $(current_page),\n\t\t\ttarget_page: $(network_page)\n\t\t});\n\t\tif (event.stopped == true) {\n\t\t\t// an observer stopped this event... don't open network dialog.\n\t\t\tEvent.stop(event);\n\t\t\tevent.preventDefault();\n\n\t\t\treturn false;\n\t\t}\n\n\t\tif (jQuery(\".main_content.network_page\").hasClass(\"loading\") ) {\n\t\t\tcreatePage(\"network_page\");\n\t\t}\n\t\tupdateNetworkPageControls(); // see network_window.js...\n\t\tjQuery(\"#network_page\").dialog({ title:'Network Configuration', modal:true, width: 600, dialogClass: 'dialog_config', resizable:false });\n\t});\n\n\tjQuery(\".wireless_icon\").click(function(event) {\n\t\tif (jQuery(\".main_content.wireless_page\").hasClass(\"loading\") ) {\n\t\t\tcreatePage(\"wireless_page\");\n\t\t}\n\t\tupdateWirelessPageControls();\t// see network_window.js...\n\t\tjQuery(\"#wireless_page\").dialog({ title:'Wireless Configuration', modal:true, width: 600, position:'center', dialogClass: 'dialog_config', resizable:false });\n\n\t});\n\n\tjQuery(window).resize(function() {\n\t\t// BG fixed exceptions being thrown when resized happened if dialog(s) were hidden (affects IE)\n\t\tvar dialogs = jQuery(\".ui-dialog-content:visible\");\n\t\tif (dialogs){\n\t\t\tjQuery.each(dialogs, function () {\n\t            var dialog = jQuery(this).data(\"uiDialog\");\n\t            if (dialog.options.position) {\n\t                dialog.option(\"position\", dialog.options.position);\n\t            }\n\t\t\t});\n\t\t}\n\t});\n\n\tfunction isNumericKeypress(value) {\n\t\ttry {\n\t\t\tvar validator = new RegExp(\"[0-9]\");\n\t\t\treturn validator.test(value);\n\t\t} catch (e) {}\n\t\treturn false;\n\t}\n\n\tvar CreateMappingConfig = function() {\n\t\tif (descriptors_created == true) {\n\t\t\tjQuery.ajax({\n\t\t\t\turl:\"/map.json\",\n\t\t\t\tdataType:\"json\",\n\t\t\t\tsuccess: function(data,resulttext,xhr) {\n\t\t\t\t\tmapping_config = new MappingConfig(data,config);\n\t\t\t\t\tdebug('MappingConfig finished loading.');\n\t\t\t\t\tfirmware_load();\n\t\t\t\t\tjQuery.publish('aja:mapper:loaded', [ mapping_config ]);\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\telse { setTimeout(CreateMappingConfig,100); }\n\t};\n\n\ttry {\n\t\tvar uri = \"/config?action=connect\";\n\t\tconfig = new Config(uri);\n\n\t} catch (e) {}\n\n\tCreateMappingConfig();\n\n\t// With the introduction of the scheduler, the status page can have URLs which are single \"words\"\n\t// that are 80-150+ characters long. This causes premature horizontal scrolling.\n\t// In combination with a word-wrap:break-word; css style and watching the page for a resize event,\n\t// we adjust the .readonly elements on the #status_page to be just the right size, forcing a wrap\n\t// in the mid-ranges, and avoiding an unfriendly horizontal scroll.\n\t// The negative user experience of the alternatives makes this hack seem worth the trade-off.\n\tvar $statusPage = jQuery('#status_page');\n\tvar ensureStatusValueMaxWidths = function()\n\t{\n\t\t// We set the value div to the width of the status page container minus the labels on the left.\n\t\t$statusPage.find('.readonly').css({ maxWidth: ($statusPage.width() - 210) + 'px' });\n\t};\n\t// Register...\n\tjQuery(window).resize(jQuery.debounce(250, ensureStatusValueMaxWidths));\n\t// ...and call on page ready.\n\tensureStatusValueMaxWidths();\n});\n\n\t</script>\n\n    <!-- transport_button.js was included here -->\n\n\t<!--\n\t<script type=\"text/javascript\">\n\t /*\n  * AJA: BG- changes for JQuery-1.11.0 obsolete API (old code commented and marked with //BG\n  */\nvar auto_refresh = false;\n\nvar g_hide_text = \"Hide Gang View\";\nvar g_show_text = \"Show Gang View\";\n\nfunction display_device_listing(forceDiscover,really) {\n\thide_gang_view();\n\tshow_gang_view();\n}\n\nfunction refresh_device_listing(event) {\n\t\n\tif (event.ctrlKey || event.shiftKey) {\n\t\tauto_refresh = !auto_refresh; // toggle state of auto-refresh\n\t\tvar manual = $(\"manual_refresh\");\n\t\tif (manual && auto_refresh == true) {\n\t\t\tmanual.hide();\n\t\t}\n\t\telse\n\t\t{\n\t\t\tmanual.show();\n\t\t}\n\t}\n\tdisplay_device_listing(true,\"really\"); // manually clicking always forces discovery (really=really)\t\t\n}\n\nfunction auto_refresh_device_listing(pe) {\n\tif (auto_refresh)\n\t{\n\t\tdisplay_device_listing(true,null); // auto refresh doesn't do really=really\t\t\n\t}\n}\n\nfunction clear_master() {\n\tvar masters = find_master(); // find_master returns an array containing the selected masters\n\tchange_master_status(null);  // change all of them to not be masters\n\n\t// update the UI \n\tif (masters) {\n\t\tjQuery(masters).each( function (index,element) {\n\t\t\tjQuery(element).prop(\"checked\",false);\n\t\t\tupdate_settings_view(element);\n\t\t});\n\t}\n\n\tupdate_master_header(null);\n}\n\nfunction set_gang_member_status(element,status)\n{\n\tvar param_id = jQuery.paramid(element);\n\tvar url = get_uri_prefix(element) + \"/config?action=set&configid=0&paramid=\" + param_id;\n\ttry {\n\t\tvar value = status?\"1\":\"0\";\n\t\tjQuery.ajax({\n\t\t\t  dataType: 'jsonp',\n\t\t\t  url: url + \"&value=\" + value,\n\t\t\t  success: function (data,status) {\n\t\t\t  \tupdate_param_checkbox(element,data,status);\n\t\t\t\tupdate_settings_view(element);\n\t\t\t  },\n\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t  }\n\t\t});\n\t} catch (e) {\n\t\t//alert(\"exception: \" + e);\n\t\treturn false;\n\t}\n}\n\nfunction select_gang()\n{\n\tjQuery(\".eParamID_GangEnable\").prop(\"checked\",true);\n\tjQuery(\".eParamID_GangEnable\").each( function (index,element) {\n\t\tset_gang_member_status(element,true);\n\t});\n  \tupdate_gang_list();\n}\n\nfunction clear_gang()\n{\n\tjQuery(\".eParamID_GangEnable\").prop(\"checked\",false);\n\tjQuery(\".eParamID_GangEnable\").each( function (index,element) {\n\t\tset_gang_member_status(element,false);\n\t});\n  \tupdate_gang_list();\n}\n\nfunction find_master() {\n\tvar master = jQuery(\".eParamID_GangMaster:checked\");\n\treturn master;\n}\n\nfunction find_context(element) {\n\tvar context = jQuery(element).parents(\".borg_context:first\");\n\treturn context;\n}\n\n// UTILITY FUNCTIONS\nfunction get_uri_prefix(element) {\n\tvar panel = jQuery(element).parents(\".borg_context\");\n\tvar prefix = panel.find(\"a.borg:first\").attr(\"href\");\n\treturn prefix;\n}\n\nfunction get_paramid_from_element(element) {\n\tvar classes = new String(jQuery(element).attr(\"class\"));\n\tif (classes.length == 0){\n\t\treturn null;\n\t}\n\tvar classes = classes.split(\" \");\n\tfor (var index = 0; index < classes.length; index++) {\n\t\tvar str = classes[index];\n\t\tif (str.indexOf(\"eParamID_\") != -1) {\n\t\t\treturn str;\n\t\t}\n\t}\n\treturn null;\n}\n\n// PER-CONTROL FUNCTIONS\nfunction update_param_checkbox(element,data,status)\n{\n\ttry {\n\t\tif (data.value == 1) {\n\t\t\tjQuery(element).prop(\"checked\",true);\n\t\t} else {\n\t\t\tjQuery(element).prop(\"checked\",false);\n\t\t}\n\t\t\n\t\tjQuery(\".borg\").each( function (index, element) {\n\t\t\tupdate_settings_view(element);\n\t\t});\n\t} catch (e) {\n\t\t// alert(\"update_element exception: \" + e);\n\t}\n}\n\nfunction isGangEnabled(context) {\n\tvar ganged = jQuery(context).find(\".eParamID_GangEnable\").prop(\"checked\");\n\treturn ganged;\n}\n\nfunction isMaster(context) {\n\tvar master = jQuery(context).find(\".eParamID_GangMaster\").prop(\"checked\");\n\treturn master;\n}\n\nfunction get_gang_clipname() {\n\tvar master = find_master();\n\tif (master && isGangEnabled(find_context(master))) {\n\t\tvar master_context = find_context(master);\n\t\tvar useGangClipName = jQuery(master_context).find(\".eParamID_UseGangClipName\").text();\n\t\tif (useGangClipName == \"Master Name\") {\n\t\t\tvar master_clip = jQuery(master_context).find(\".master_clip_name:first\").text();\n\t\t\treturn master_clip;\n\t\t} else {\n\t\t\tvar custom_clip = jQuery(master_context).find(\".eParamID_CustomClipName:first\").text();\n\t\t\treturn custom_clip;\n\t\t}\n\t}\n\n\treturn \"No Gang Master Defined\";\n}\n\nfunction refresh_gang_clipname() {\n\tvar gang_clip = get_gang_clipname();\n\tjQuery(\".borg_context\").each( function (index,context_element) {\n\t\tvar gang_name_element = jQuery(context_element).find(\".gang_clip_name\");\n\t\tjQuery(gang_name_element).text(gang_clip);\n\t});\n}\n\nfunction is_being_edited(element) {\n\treturn (element.editing && element.editing == true);\n}\n\nfunction refresh_param_text_elements() {\n\tjQuery(\".param_text\").each( function (index,element) {\n\t\tif (is_being_edited(element))\n\t\t\treturn;\n\t\t\n\t\tvar param_id = get_paramid_from_element(element); // this refers to the element selected\n\t\tif (param_id == null)\n\t\t\treturn;\n\n\t\tvar url = get_uri_prefix(element) + \"/config?action=get&configid=0&paramid=\" + param_id;\n\t\ttry {\n\t\t\tjQuery.ajax({\n\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t  url: url,\n\t\t\t\t  success: function (data,status) {\n\t\t\t\t  \tupdate_param_text(element,data,status);\n\t\t\t\t  },\n\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t  }\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\t//alert(\"exception: \" + e);\n\t\t\treturn false;\n\t\t}\n\t});\n}\n\nfunction refresh_param_checkboxes() {\n\tjQuery('.param_checkbox').each( function (index,element) {\n\t\tif (is_being_edited(element))\n\t\t\treturn;\n\t\t\n\t\tvar param_id = get_paramid_from_element(this); // this refers to the element selected\n\t\tif (param_id == null)\n\t\t\treturn;\n\n\t\tvar element = this;\n\t\tvar url = get_uri_prefix(element) + \"/config?action=get&configid=0&paramid=\" + param_id;\n\t\ttry {\n\t\t\tjQuery.ajax({\n\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t  url: url,\n\t\t\t\t  success: function (data,status) {\n\t\t\t\t  \tupdate_param_checkbox(element,data,status);\n\t\t\t\t  },\n\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t  }\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\t//alert(\"exception: \" + e);\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t});\n}\n\nfunction refresh_param_radios() {\n\tjQuery('.param_radio').each( function (index, element) {\n\t\tif (is_being_edited(element))\n\t\t\treturn;\n\t\t\n\t\tvar param_id = get_paramid_from_element(this); // this refers to the element selected\n\t\tif (param_id == null)\n\t\t\treturn;\n\n\t\tvar element = this;\n\t\tvar url = get_uri_prefix(this) + \"/config?action=get&configid=0&paramid=\" + param_id;\n\t\ttry {\n\t\t\tjQuery.ajax({\n\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t  url: url,\n\t\t\t\t  success: function (data,status) {\n\t\t\t\t  \tupdate_param_radio(element,data,status);\n\t\t\t\t  },\n\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t  }\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\t//alert(\"exception: \" + e);\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t});\n}\n\nfunction update_master_header(element) {\n\n\ttry {\n\t\tif (element == null) {\n\t\t\tjQuery(\"#master_header\").html(\"&nbsp;\"); // nobody is the master...\n\t\t\treturn;\n\t\t}\n\t\t\n\t\tvar prefix = get_uri_prefix(element);\n\t\tvar hostname = window.location.hostname;\n\t\tif (prefix.indexOf(hostname) != -1) {\n\t\t\t// we are master!!!\n\t\t\tjQuery(\"#master_header\").html(\"MASTER\");\n\t\t} else {\n\t\t\t// we are not...\n\t\t\tjQuery(\"#master_header\").html(\"&nbsp;\");\n\t\t}\n\t} catch (e) { /*alert(e);*/ }\n}\n\nfunction update_settings_view(element) {\n\tvar context = jQuery(element).parents(\".borg_context:first\");\n\n\tif (is_being_edited(element))\n\t\treturn;\n\t\n\tif (!isGangEnabled(context)) {\n\t\tjQuery(context).find(\".loser_settings\").show();\n\t\tjQuery(context).find(\".master_settings\").hide();\n\t\tjQuery(context).find(\".slave_settings\").hide();\n\t} else if (isMaster(context)) {\n\t\tjQuery(context).find(\".loser_settings\").hide();\n\t\tjQuery(context).find(\".slave_settings\").hide();\n\n\t\tjQuery(context).find(\".master_settings\").show();\n\t\tvar useCustomClipName = jQuery(context).find(\".eParamID_UseCustomClipName\").text(); // WARNING: FRAGILE LOGIC...\n\t\tif (useCustomClipName == \"OFF\") { // WARNING: FRAGILE LOGIC...\n\t\t\t// standard\n\t\t\tjQuery(context).find(\".master_settings\").find(\".custom_name\").hide();\n\t\t\tjQuery(context).find(\".master_settings\").find(\".standard_name\").show();\n\t\t} else {\n\t\t\t// custom\n\t\t\tjQuery(context).find(\".master_settings\").find(\".custom_name\").show();\n\t\t\tjQuery(context).find(\".master_settings\").find(\".standard_name\").hide();\n\t\t}\n\t\tupdate_master_header(element);\n\t\t\n\t} else {\n\t\tjQuery(context).find(\".loser_settings\").hide();\n\t\tjQuery(context).find(\".master_settings\").hide();\n\t\tvar gang_clip = get_gang_clipname();\n\t\tvar gang_name_element = jQuery(context).find(\".gang_clip_name\");\n\t\tjQuery(gang_name_element).text(gang_clip); \n\t\tjQuery(context).find(\".slave_settings\").show();\n\t}\n}\n\nfunction update_param_radio(element,data,status)\n{\n\ttry {\n\t\tif (data.value == 1) {\n\t\t\t// this element is the master... turn the other ones off first\n\t\t\tjQuery(\".param_radio\").prop(\"checked\",false);\n\t\t\t\n\t\t\tjQuery(element).prop(\"checked\",true);\n\t\t} else {\n\t\t\tjQuery(element).prop(\"checked\",false);\n\t\t}\n\n\t\tjQuery(\".borg\").each( function (index, element) { update_settings_view(element); } );\n\t\t\n\t} catch (e) {\n\t\t//alert(\"update_element exception: \" + e);\n\t}\n}\n\n// Not used?\nfunction update_param_text(element,data,status)\n{\n\ttry {\n\t\tvar paramid = get_paramid_from_element(element);\n\t\tif (paramid == \"eParamID_CurrentClip\") {\n\t\t\tdata.value_name = data.value_name.replace(\".mov\",\"\");\n\t\t\tdata.value_name = data.value_name.replace(\".MOV\",\"\"); // other permutations?\n\t\t}\n\t\tvar text = data.value_name;\n\t\t\n\t\tif (paramid == \"eParamID_ClipNumber\" && +text == 0) {\n\t\t\ttext = \"None\";\n\t\t}\n\n\t\t// postfix handling\n\t\tvar descriptor = jQuery(element).descriptor();\n\t\tif (descriptor && descriptor.units) {\n\t\t\tif (descriptor.units != \"\") {\n\t\t\t\ttext = text + descriptor.units; // append units postfix\n\t\t\t}\n\t\t}\n\t\tjQuery(element).text(text); // use .val(text); ???\n\t} catch (e) {\n\t\t//alert(\"update_element exception: \" + e);\n\t}\n}\n\nfunction get_ip_address(element) {\n\tvar uri = get_uri_prefix(element);\n\tvar ip = uri.replace(\"http://\",\"\",\"gi\");\n\treturn ip;\n}\n\nfunction update_gang_list() {\n\tvar master = find_master();\n\tif (master.length == 0) {\n\t\treturn; // no master to update...\n\t}\n\tvar master_ipaddr = get_ip_address(master);\n\tif (master_ipaddr == null) {\n\t\treturn; // huh?\n\t}\n\t\n\tvar gang_members = jQuery('.eParamID_GangEnable:checked');\n\tvar gang_list = \"\";\n\tfor(var index = 0; index < gang_members.length; index++) {\n\t\tvar ipaddr = get_ip_address(gang_members[index]);\n\t\tif (ipaddr == master_ipaddr) {\n\t\t\tcontinue; // don't add the master to the list\n\t\t}\n\t\t\n\t\tif (index > 0 && gang_list.trim().length > 0)\n\t\t\tgang_list = gang_list + \", \";\n\t\tgang_list = gang_list + ipaddr;\n\t}\n\n\t// send it to the master (only?)\n\tif (master) {\n\t\tvar url = get_uri_prefix(master) + \"/config?action=set&configid=0&paramid=eParamID_GangList&value=\" + gang_list;\n\t\ttry {\n\t\t\tjQuery.ajax({\n\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t  url: url,\n\t\t\t\t  success: function (data,status) {\n\t\t\t\t  \t// update_param_checkbox(element,data,status);\n\t\t\t\t  },\n\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t  }\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\t//alert(\"exception: \" + e);\n\t\t\treturn false;\n\t\t}\n\t}\n}\n\nfunction initialize_param_checkbox(row,options) {\n\tjQuery(row).find('.param_checkbox').each( function (index) {\n\t\t\n\t\tvar param_id = get_paramid_from_element(this); // this refers to the element selected\n\t\tif (param_id == null)\n\t\t\treturn;\n\n\t\tvar element = this;\n\t\t\n\t\tvar url = get_uri_prefix(element) + \"/config?action=get&configid=0&paramid=\" + param_id;\n\t\ttry {\n\t\t\tjQuery.ajax({\n\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t  url: url,\n\t\t\t\t  success: function (data,status) {\n\t\t\t\t  \tupdate_param_checkbox(element,data,status);\n\t\t\t\t  },\n\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t  }\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\t//alert(\"exception: \" + e);\n\t\t\treturn false;\n\t\t}\n\n\t\t// register for click events\n\t\tvar url = get_uri_prefix(element) + \"/config?action=set&configid=0&paramid=\" + param_id;\n\t\tjQuery(element).click( function (event) {\n\t\t\ttry {\n\t\t\t\tvar value = jQuery(event.target).prop(\"checked\");\n\t\t\t\tvalue = (value == true)?\"1\":\"0\";\n\t\t\t\tjQuery.ajax({\n\t\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t\t  url: url + \"&value=\" + value,\n\t\t\t\t\t  success: function (data,status) {\n\t\t\t\t\t  \tupdate_param_checkbox(element,data,status);\n\t\t\t\t\t  \tupdate_gang_list();\n\t\t\t\t\t  },\n\t\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t\t  }\n\t\t\t\t\t});\n\t\t\t\treturn true;\n\t\t\t} catch (e) {\n\t\t\t\t//alert(\"exception: \" + e);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t});\n\n\t\t// register for param changed events\n\t\tjQuery(element).bindParamChangedEvent( function (event,config_event) {\n\t\t\ttry {\n\t\t\t\tvar value = config_event.int_value;\n\t\t\t\tif (value == 1) {\n\t\t\t\t\tjQuery(element).prop(\"checked\",true);\n\t\t\t\t} else {\n\t\t\t\t\tjQuery(element).prop(\"checked\",false);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tjQuery(\".borg\").each( function (index, element) {\n\t\t\t\t\tupdate_settings_view(element);\n\t\t\t\t});\n\t\t\t} catch (e) {\n\t\t\t\t// alert(\"update_element exception: \" + e);\n\t\t\t}\n\t\t},options);\n\n\t\treturn true;\n\t});\n}\n\n/* FIXME: RecordFormat + FrameRate + EncodeType + SlowMotion\n * see: function get_rec_str_from(recordFormat, encodeType, sloMo) - in aja_utils.js\nfunction update_record_format(element, data, status) {\n\t\n}\n\nfunction initialize_record_format(row,options) {\n\tjQuery(row).find('.param_record_format').each( function (index) {\n\t\t\n\t\tvar param_id = get_paramid_from_element(this); // this refers to the element selected\n\t\tif (param_id == null)\n\t\t\treturn;\n\n\t\tvar element = this;\n\t\t\n\t\tvar url = get_uri_prefix(element) + \"/config?action=get&configid=0&paramid=\" + param_id;\n\t\ttry {\n\t\t\tjQuery.ajax({\n\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t  url: url,\n\t\t\t\t  success: function (data,status) {\n\t\t\t\t  \tupdate_record_format(element,data,status);\n\t\t\t\t  },\n\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t  }\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\t//alert(\"exception: \" + e);\n\t\t\treturn false;\n\t\t}\n\n\t\t// register for param changed events\n\t\tjQuery(element).bindParamChangedEvent( function (event,config_event) {\n\t\t\ttry {\n\t\t\t\tvar value = config_event.int_value;\n\t\t\t\tif (value == 1) {\n\t\t\t\t\tjQuery(element).prop(\"checked\",true);\n\t\t\t\t} else {\n\t\t\t\t\tjQuery(element).prop(\"checked\",false);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tjQuery(\".borg\").each( function (index, element) {\n\t\t\t\t\tupdate_settings_view(element);\n\t\t\t\t});\n\t\t\t} catch (e) {\n\t\t\t\t// alert(\"update_element exception: \" + e);\n\t\t\t}\n\t\t},options);\n\n\t\treturn true;\n\t});\n}\n*/\n\nfunction change_master_status(master) {\n\tjQuery('.param_radio').each( function (index) {\n\t\tvar param_id = get_paramid_from_element(this); // this refers to the element selected\n\t\tif (param_id == null)\n\t\t\treturn;\n\t\t\n\t\tvar element = this;\n\t\tvar url = get_uri_prefix(element) + \"/config?action=set&configid=0&paramid=\" + param_id;\n\t\tvar isMaster = ((master != null) && (master == element));\n\t\tvar value = isMaster?\"1\":\"0\";\n\t\tjQuery.ajax({\n\t\t\t  dataType: 'jsonp',\n\t\t\t  url: url + \"&value=\" + value,\n\t\t\t  success: function (data,status) {\n\t\t\t  \tupdate_param_radio(element,data,status);\n\t\t\t  },\n\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t  }\n\t\t});\n\t\t\n\t\tif (isMaster) {\n\t\t\t// update the gang member status - first find the gang member checkbox\n\t\t\tvar context = find_context(element);\n\t\t\tvar gang_enable_element = jQuery(context).find(\".eParamID_GangEnable\");\n\t\t\tgang_enable_element.prop(\"checked\", true).triggerHandler(\"change\");\n\t\t}\n\t});\n\tsetTimeout(update_gang_list, 1000); // this has most likely changed and the new master needs it (also)\n}\n\nfunction initialize_param_radio(row,options) {\n\tjQuery(row).find('.param_radio').each( function (index) {\n\t\tvar param_id = get_paramid_from_element(this); // this refers to the element selected\n\t\tif (param_id == null)\n\t\t\treturn;\n\n\t\tvar element = this;\n\t\tvar url = get_uri_prefix(element) + \"/config?action=get&configid=0&paramid=\" + param_id;\n\t\ttry {\n\t\t\tjQuery.ajax({\n\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t  url: url,\n\t\t\t\t  success: function (data,status) {\n\t\t\t\t  \tupdate_param_radio(element,data,status);\n\t\t\t\t  },\n\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t  }\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\t//alert(\"exception: \" + e);\n\t\t\treturn false;\n\t\t}\n\n\t\t// register for click events\n\t\tjQuery(element).click( function (event) {\n\t\t\ttry {\n\t\t\t\tvar element = this;\n\t\t\t\tvar isNewMaster = jQuery(event.target).prop(\"checked\");\n\t\t\t\tif (isNewMaster) {\n\t\t\t\t\tchange_master_status(element);\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t} catch (e) {\n\t\t\t\t//alert(\"exception: \" + e);\n\t\t\t\treturn false;\n\t\t\t}\n\t\t});\n\t\t\n\t\treturn true;\n\t});\n}\n\nfunction initialize_param_text(row,options) {\n\tjQuery(row).find('.param_text').each( function (index) {\n\t\tvar param_id = get_paramid_from_element(this); // this refers to the element selected\n\t\tif (param_id == null)\n\t\t\treturn;\n\n\t\tvar element = this;\n\t\tvar url = get_uri_prefix(element) + \"/config?action=get&configid=0&paramid=\" + param_id;\n\t\ttry {\n\t\t\tjQuery.ajax({\n\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t  url: url,\n\t\t\t\t  success: function (data,status) {\n\t\t\t\t  \tupdate_param_text(element,data,status);\n\t\t\t\t  },\n\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t  }\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\t//alert(\"exception: \" + e);\n\t\t\treturn false;\n\t\t}\n\n\t\tif (jQuery(element).hasClass(\"editable\")) {\n\t\t\tmake_editable_text(element);\n\t\t}\n\t\t\t\n\t\treturn true;\n\t});\n}\n\nfunction refresh_param_selects() {\n\tjQuery(\".param_select\").each( function (index, element) {\n\t\tif (is_being_edited(element))\n\t\t\treturn;\n\t\t\n\t\tvar param_id = get_paramid_from_element(this); // this refers to the element selected\n\t\tif (param_id == null)\n\t\t\treturn;\n\n\t\tvar element = this;\n\t\tvar url = get_uri_prefix(this) + \"/config?action=get&configid=0&paramid=\" + param_id;\n\t\ttry {\n\t\t\tjQuery.ajax({\n\t\t\t\t  dataType: 'jsonp',\n\t\t\t\t  url: url,\n\t\t\t\t  success: function (data,status) {\n\t\t\t\t  \tupdate_param_text(element,data,status);\n\t\t\t\t  \tupdate_settings_view(element);\n\t\t\t\t  },\n\t\t\t\t  error: function(XMLHttpRequest, textStatus, errorThrown) {\n\t\t\t\t\t  //alert(\"error \" + XMLHttpRequest.status + \", \" + XMLHttpRequest.statusText + \", \" + textStatus + \", \" + errorThrown);\n\t\t\t\t  }\n\t\t\t\t});\n\t\t} catch (e) {\n\t\t\t//alert(\"exception: \" + e);\n\t\t\treturn false;\n\t\t}\n\t});\n}\n\nfunction refresh_local_data() {\n\tjQuery(\".borg\").each( function (index, element) { update_settings_view(element); });\n}\n\nfunction prePageNavigateGangPage(event)\n{\n\tvar page  = event.memo.target_page.id;\n\tvar transport_state = mapping_config.get(\"eParamID_TransportState\");\n\tvar media_state = mapping_config.get(\"eParamID_MediaState\");\n\tif (isPageRestrictedByState(page,transport_state,media_state)) { // see nav.js for isPageRestrictedByState\n\t\ttransport_state = +transport_state;\n\t\tvar current_activity = \"Transport\";\n\t\tif (isPaused(transport_state) || isPlaying(transport_state)) {\n\t\t\tcurrent_activity = \"Playback\";\n\t\t} else if (transport_state == eTSRecording) {\n\t\t\tcurrent_activity = \"Recording\";\n\t\t}\n\t\tvar response = confirm(\"Stop \" + current_activity + \"?\" );\n\t\tif (response == true) {\n\t\t\t// stop the transporter\n\t\t\tmapping_config.set(\"eParamID_TransportCommand\", eTCStop);\n\t\t\tsetTimeout(function () { mapping_config.set(\"eParamID_TransportCommand\", eTCStop); }, 200 );  // FIXME: don't send second stop if it isn't necessary...\n\t\t\treturn event; // OK - proceed\n\t\t}\n\t\t// send them back to where they came from\n\t\tevent.memo.target_page = $(event.memo.current_page);\n\t\tEvent.stop(event);\n\t\t//jQuery(event).preventDefault();\n\t\treturn event;\n\t}\n\tshow_gang_view();\n\treturn event;\n}\n\nfunction gotoborg(href) {\n\twindow.location = href + \"#gang_page\";\n\treturn false;\n}\n\nfunction add_borg_context_row(sysname,ipaddr) {\n\tvar local = window.location.hostname;\n\tvar localClass = \"\";\n\tif(local == ipaddr )\n\t\tlocalClass = \"localhost\";\n\tvar custom_clip_take = '<tr><td style=\"text-align: right; width: 10%;\">Custom&nbsp;Take:</td><td><div class=\"param param_text eParamID_CustomTake editable\"></div></td></tr>';\n\tvar row_html = '<tr class=\"borg_context\">' +\n\t'<td class=\"system\"><a class=\"borg ' + localClass +' param param_text eParamID_SystemName\" href=\"http://' + ipaddr + '\" onclick=\"javascript: return gotoborg(event.target.href);\">' + sysname + '</a></td>' +\n\t'<td class=\"format\"><div class=\"param param_select eParamID_RecordFormat\"></div></td>' +\n\t'<td class=\"storage\"><span class=\"param param_text eParamID_CurrentMediaAvailable\"></span></td>' +\n\t'<td class=\"slave\" style=\"width: 16px;\"><input type=\"checkbox\" class=\"param param_checkbox eParamID_GangEnable\"></input></td>' +\n\t'<td class=\"master style=\"width: 16px;\"><input type=\"radio\" class=\"param param_radio eParamID_GangMaster\"></input></td>' +\n\t'<td class=\"settings\">' +\n\t\t'<div class=\"master_settings\" style=\"display: none;\">' +\n\t\t\n\t\t\t'<div>Use&nbsp;Custom&nbsp;Clip&nbsp;Name:&nbsp;<div class=\"param param_select eParamID_UseCustomClipName editable\" ></div></div>' +\n\t\t\t\n\t\t\t'<div class=\"custom_name\" style=\"display: none;\">' +\n\t\t\t\t'<table>' +\n\t\t\t\t'<tr><td style=\"text-align: right; width: 10%;\">Custom&nbsp;Clip&nbsp;Name:</td> <td><div class=\"param param_text eParamID_CustomClipName editable\"></div></td></tr>' +\n\t\t\t\tcustom_clip_take +\n\t\t\t\t'</table>' +\n\t\t\t'</div>' +\n\t\t\t\n\t\t\t'<div class=\"standard_name\" style=\"display: none;\">' +\n\t\t\t    '<table>' +\n\t\t\t\t'<tr><td style=\"text-align: right; width: 10%;\">Clip&nbsp;Name:</td>    <td><div class=\"param param_select eParamID_ClipName editable\"></div></td></tr>' +\n\t\t\t\t'<tr><td style=\"text-align: right; width: 10%;\">Clip&nbsp;Number:</td>  <td><div class=\"param param_text eParamID_ClipNumber editable\"></div></td></tr>' +\n\t\t\t\t'<tr><td style=\"text-align: right; width: 10%;\">Clip&nbsp;Append:</td>  <td><div class=\"param param_select eParamID_ClipAppend editable\"></div></td></tr>' +\n\t\t\t\t'<tr><td style=\"text-align: right; width: 10%;\">Alpha&nbsp;Append:</td> <td><div class=\"param param_select eParamID_AlphaAppend editable\"></div></td></tr>' +\n\t\t\t\t'<tr><td style=\"text-align: right; width: 10%;\">Take:</td>              <td><div class=\"param param_text eParamID_Take editable\"></div></td></tr>' +\n\t\t\t\t'</table>' +\n\t\t\t'</div>' +\n\t\t\t\n\t\t'</div>' +\n\t\t'<div class=\"slave_settings\" style=\"display: none;\">' +\n\t\t\t'<div style=\"display: inline;\">Use:&nbsp;<div style=\"display: inline;\" class=\"param param_select eParamID_UseGangClipName editable\"></div></div>' +\n\t\t'</div>' +\n\t\t'<div class=\"loser_settings\" style=\"display: none;\">' +\n\t\t\t'<div style=\"display: inline;\">Unit&nbsp;Clip&nbsp;Name:&nbsp;<div style=\"display: inline;\" class=\"param param_text eParamID_CurrentClip\"></div></div>' +\n\t\t'</div>' +\n\t'</td>' +\n\t'</tr>';\n\tvar row = jQuery(row_html); // returns the created node\n\tjQuery(\".gang > tbody\").append(row);\n\t\n\tvar options = { host: ipaddr };\n\tjQuery(\".param_select.editable\",row).make_editable_select(options);\n\tjQuery(\".param_text.editable\",row).make_editable_text(options);\n\tinitialize_param_radio(row,options);\n\tinitialize_param_checkbox(row,options);\n\n\treturn row;\n}\n\nfunction build_gang_table(borgs) {\n\tjQuery(\".gang\").hide();\n\tjQuery(\".gang\").find(\".borg_context\").remove(); // remove old entries\n\tjQuery(\"#gang_loading\").show();\n\t\n\tjQuery.each(borgs,function (index,borg) {\n\t\ttry {\n\t\t\tif (borg.gangable) { // see update_gang_members below to see how this is updated\n\t\t\t\tvar row = add_borg_context_row(borg.description,borg.ip_address); // only add gangable borgs to the table\n\t\t\t\tjQuery.connect(borg.ip_address, function () {\n\t\t\t\t\tvar options = {host:borg.ip_address};\n\t\t\t\t\tvar test_elements = jQuery(\".param\",row);\n\t\t\t\t\ttest_elements.addConfigEventListener(options);\n\t\t\t\t}); // connect\n\t\t\t}\n\t\t} catch(e) {debug(\"caught exception \" + e );}\n    });\n\n\tjQuery(\"#gang_loading\").hide();\n\tjQuery(\".gang\").show();\n\treturn;\n}\n\nvar g_borgs = [];\n\nfunction compare_borgs(a,b) {\n\tif (a.description.toUpperCase() < b.description.toUpperCase()) return -1;\n\tif (a.description.toUpperCase() > b.description.toUpperCase()) return 1;\n\treturn 0;\n}\n\n// called by network_page.html when borg list is updated\nvar g_temp_borgs = null;\nfunction update_gang_members(borgs)\n{\n\tg_temp_borgs = borgs; // store for when user clicks gang_toggle\n\t\n\t/* no - user must manually refresh the list\n\t// do we need to update the visible (shown) gang?\n\tvar text = jQuery(\"#gang_toggle\").text();\n\tif (text == g_show_text) {\n\t\t// nope... we will update when the user clicks the gang_toggle button \n\t} else {\n\t\t// yes... update now since the view is visible\n\t\tshow_gang_view(); // since it does all the qualify stuff...\n\t}\n\t*/\n}\n\nfunction qualify_gang_members()\n{\n\t\n//\tborgs = hardcoded_host_list; // debug\n\t//borgs.sort(compare_borgs);\n\tg_borgs = g_temp_borgs;\n\tvar count_down = g_borgs.length;\n\t\n\t// we need to detect if a borg is capable of gang operations\n    var deferred_queue = [];\n\n\tjQuery.each(g_borgs, function (index,borg) {\n\n\t\t// TODO: fix this...\n\t \t// var localhost = window.location.hostname;\n\t \t// var host = borg.host_name;\n\t \t// if (host.indexOf(localhost) != -1) {\n\t \t\t// this is a local host... change hostname to local version... an empty string...\n\t \t// \tborg.host_name = \"\";\n\t \t// }\n\t\t\n\t\t//var borg = g_borgs[index];\n\t\tjQuery.extend(borg, {\"gangable\":false}); // default false\n\n\t\tvar localhost = window.location.hostname; // ip_address?\n\t\tvar remotehost = borg.ip_address;\n\t\t// debug(\"qualifying host \" + remotehost);\n\t\tif (remotehost == localhost)\n\t\t{\n\t\t\t// our host...\n\t\t\t// debug(\"remotehost == localhost - gangable = true\");\n\t\t\tjQuery.extend(g_borgs[index],{\"gangable\":true});\n\t\t}\n\t\telse\n\t\t{\n\t\t\t// remote host...\n\t\t\tvar product_id = \"Unknown\";\n\t\t\tif (typeof(mapping_config) != \"undefined\" && mapping_config != null) {\n\t\t\t\tproduct_id = mapping_config.get(\"eParamID_ProductID\");\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\talert(\"unable to get product ID from our host\");\n\t\t\t}\n\t\t\t\n\t\t \t//var timeout = (60*1000); // TODO: tune this...\n\t\t\tvar prefix = jQuery.prefix(String(borg.ip_address));\n\t\t\tvar url = prefix + \"/config?action=get&configid=0&paramid=eParamID_ProductID\";\n\t\t\tvar deferred = jQuery.ajax({\n\t\t\t\turl: url,\n\t\t\t\ttimeout: 1000,\n\t\t\t\tdataType: (prefix == \"\") ? 'json' : 'jsonp',\n\t\t\t\tsuccess: function (data) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tvar gang_member_product_id = data.value;\n\t\t\t\t\t\tif (gang_member_product_id == product_id) {\n\t\t\t\t\t\t\t// debug(\"remotehost == localhost - gangable = true\");\n\t\t\t\t\t\t\tjQuery.extend(g_borgs[index],{\"gangable\":true});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// debug(\"remotehost == localhost - gangable = default (false)\");\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\tdebug(\"CAUGHT EXCEPTION: \" + e);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\terror: function(request, textStatus, errorThrown) {\n\t\t\t\t},\n\t\t\t\tcomplete: function() {\n\t\t\t\t\t// check to see if we are the last... if so, \n\t\t\t\t\tif (count_down-- <= 0)\n\t\t\t\t\t{\n\t\t\t\t\t\t// all the requests have completed (or failed)\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t// g_borgs = borgs; // done with updates to gangable\n\t\t\t\t\t\t} catch(e) {\n\t\t\t\t\t\t\tdebug(\"update - caught exception \" + e );\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}); // ajax\n\t\t\tdeferred_queue.push(deferred);\n\t\t}\n\t}); // each\n\n    var master_deferred = jQuery.when.apply(null, deferred_queue);\n\treturn master_deferred;\n}\n\nfunction dump_borgs()\n{\n\tjQuery.each(g_borgs, function (index,borg) {\n\t\tvar gangable = false;\n\t\tif (borg && borg.gangable) {\n\t\t\tgangable = true;\n\t\t}\n\t\tdebug(\"borgs[\" + index + \"] = \" + borg.host_name + \" (\" + borg.ip_address + \"), gangable = \" + gangable);\n\t});\n}\n\n/*\nfunction refresh_gang_view(event)\n{\n\t// see network_page.html for this method\n\tdisplay_device_listing(true,\"really\",function (borgs) {\n\t\tupdate_gang_members(borgs,build_gang_table);\n\t});\n}\n*/\n\nfunction show_gang_view()\n{\n\ttry {\n\t\tjQuery(\"#gang_toggle\").text(g_hide_text).blur();\n\t\tjQuery(\"#gang_view\").show();\n\t\tif (jQuery(\".borg_context\").length == 0) {\n\t\t\tqualify_gang_members().always( function () {\n\t\t\t\tsetTimeout( function () { build_gang_table(g_borgs); }, 1000); // delay... debugging defect #4903\n\t\t\t});\n\t\t}\n\t} catch(e) {\n\t\tdebug(\"show_gang_view caught exception:\" + e );\n\t}\n\treturn false;\n}\n\nfunction hide_gang_view()\n{\n\tjQuery(\"#gang_view\").hide();\n\tjQuery.disconnect_all();\n\tjQuery(\".borg_context\").remove(); // remove all table entries\n\tjQuery(\"#gang_toggle\").text(g_show_text).blur();\n\treturn false;\n}\n\nfunction refresh_gang_view()\n{\n\ttry {\n\t\tjQuery.disconnect_all();\n\t\tjQuery(\".borg_context\").remove(); // remove all table entries\n\t\tqualify_gang_members().always( function () {\n\t\t\tsetTimeout( function () { build_gang_table(g_borgs); }, 1000); // delay... debugging defect #4903\n\t\t});\n\t} catch(e) {\n\t\tdebug(\"refresh_gang_view caught exception:\" + e );\n\t}\n\treturn false;\n}\n\nfunction isNumericKeypress(value) {\n\ttry {\n\t\tvar validator = new RegExp(\"[0-9]\");\n\t\treturn validator.test(value);\n\t} catch(e) {}\n\treturn false;\n}\n\nfunction isValidTakeNumber(value) {\n\tif (String(value).toLowerCase() == \"none\") {\n\t\tvalue = 0;\n\t}\n\tvalue = +value;\n\treturn (value >= 0 && value <= 999);\n}\n\nvar _match_length = 0;\nfunction onKeypressClipNumber(event) {\n\tvar element = event.target; // this is the input element\n\tif (element) {\n\t\tif (event.which == 8) {\n\t\t\t_match_length--;\n\t\t\treturn true; // backspace is ok\n\t\t}\n\t\t\n\t\tif (event.which == 0) return true; // special keypress is ok\n\t\t\n\t\tif (event.which == 13) {\n\t\t\t// this is a cr - the editable plugin will do a commit when it gets one of these... (TODO: WHAT ABOUT BLUR BEHAVIOR?)\n\t\t\tvar value = jQuery(element).val();\n\t\t\tif (!isValidTakeNumber(value)) {\n\t\t\t\talert(\"Invalid Take Number: \" + value);\n\t\t\t\tevent.stopImmediatePropagation();\n\t\t\t\tevent.preventDefault();\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\t\t\n\t\tvar keypress_value = String.fromCharCode(event.which);\n\t\t\n\t\t// \"matching\" none\n\t\tvar match_value = String(keypress_value);\n\t\tmatch_value = match_value.toLowerCase();\n\t\tif (_match_length == 0 && match_value == 'n') {\n\t\t\t_match_length++;\n\t\t} else if (_match_length == 1 && match_value == 'o') {\n\t\t\t_match_length++;\n\t\t} else if (_match_length == 2 && match_value == 'n') {\n\t\t\t_match_length++;\n\t\t} else if (_match_length == 3 && match_value == 'e') {\n\t\t\t_match_length++;\n\t\t} else {\n\t\t\t_match_length = 0;\n\t\t}\n\t\t\n\t\tvar is_part_of_none = (_match_length > 0);\n\t\tvar isValid = isNumericKeypress(keypress_value);\n\t\tif (!isValid && !is_part_of_none) {\n\t\t\tevent.stopImmediatePropagation();\n\t\t\tevent.preventDefault();\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nfunction onKeypressTakeNumber(event) {\n\tvar element = event.target; // this is the input element\n\tif (element) {\n\t\tif (event.which == 8) return true; // backspace is ok\n\t\tif (event.which == 0) return true; // special keypress is ok\n\t\t\n\t\tif (event.which == 13) {\n\t\t\t// this is a cr - the editable plugin will do a commit when it gets one of these... (TODO: WHAT ABOUT BLUR BEHAVIOR?)\n\t\t\tvar value = jQuery(element).val();\n\t\t\tif (!isValidTakeNumber(value)) {\n\t\t\t\talert(\"Invalid Take Number: \" + value);\n\t\t\t\tevent.stopImmediatePropagation();\n\t\t\t\tevent.preventDefault();\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\t\t\n\t\tvar keypress_value = String.fromCharCode(event.which);\n\t\tvar isValid = isNumericKeypress(keypress_value);\n\t\tif (!isValid) {\n\t\t\tevent.stopImmediatePropagation();\n\t\t\tevent.preventDefault();\n\t\t\treturn false;\n\t\t}\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nfunction isValidClipName(name)\n{\n\tvar x = /^([^\"\\(\\)?\\/*\\';<>'\\\\\\|}{\\v])*$/;\n\tvar RegEx = new RegExp(x);\n\treturn name.match(RegEx);\n}\n\nfunction isValidClipNameKeypress(key)\n{\n\tvar x = /^([^\"\\(\\)?\\/*\\';<>'\\\\\\|}{\\v])*$/;\n\tvar RegEx = new RegExp(x);\n\treturn key.match(RegEx);\n}\n\nfunction onKeypressCustomClipName(event) {\n\ttry {\n\t\tvar element = event.target; // this is the input element\n\t\tif (element) {\n\t\t\tif (event.which == 8) return true; // backspace is ok\n\t\t\tif (event.which == 0) return true; // special keypress is ok\n\t\t\t\n\t\t\tif (event.which == 13) {\n\t\t\t\t// this is a cr - the editable plugin will do a commit when it gets one of these... (TODO: WHAT ABOUT BLUR BEHAVIOR?)\n\t\t\t\tvar value = jQuery(element).val();\n\t\t\t\tif (!isValidClipName(value)) {\n\t\t\t\t\talert(\"Invalid Custom Clip Name: \" + value);\n\t\t\t\t\tevent.stopImmediatePropagation();\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\t\n\t\t\tvar keypress_value = String.fromCharCode(event.which);\n\t\t\tvar isValid = isValidClipNameKeypress(keypress_value);\n\t\t\tif (!isValid) {\n\t\t\t\tevent.stopImmediatePropagation();\n\t\t\t\tevent.preventDefault();\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t} catch(e) { alert(e); }\n}\n\nfunction initialize_master_header_listeners()\n{\n\tif (mapping_config != null) {\n\t\t\n\t\tmapping_config.listen(\"eParamID_RegisterRecall\", function (paramid, value) {\n\t\t\t// hide_gang_view();\n\t\t\tif (+value == 0) {\n\t\t\t\t// this is a factory reset...\n\t\t\t\t// we are not...\n\t\t\t\tjQuery(\"#master_header\").html(\"&nbsp;\");\n\t\t\t}\n\t\t});\n\t\tmapping_config.listen(\"eParamID_FactorySettings\", function (paramid, value) {\n\t\t\t// hide_gang_view();\n\t\t\tif (+value == 0) {\n\t\t\t\t// this is a factory reset...\n\t\t\t\t// we are not...\n\t\t\t\tjQuery(\"#master_header\").html(\"&nbsp;\");\n\t\t\t}\n\t\t});\n\t\tmapping_config.listen(\"eParamID_GangMaster\", function (paramid, value) {\n\t\t\tif (+value == 1) {\n\t\t\t\t// we are master!!!\n\t\t\t\tjQuery(\"#master_header\").html(\"MASTER\");\n\t\t\t} else {\n\t\t\t\t// we are not...\n\t\t\t\tjQuery(\"#master_header\").html(\"&nbsp;\");\n\t\t\t}\n\t\t});\n\t\t\n\t} else {\n\t\tsetTimeout(initialize_master_header_listeners, 2000); // keep trying...\n\t}\t\n}\n\njQuery(document).ready(function() {\n\n\tjQuery(\"#gang_refresh_button\").on(\"click\", function(){\n\t\trefresh_gang_view();\n\t});\n\t\n\tjQuery(\"#gang_loading\").hide();\n\tjQuery.waitForConfigEvents();\n\n\t// field validation\n\t// BG replaced 'live' with 'on'\n   \tjQuery(\"[class~='eParamID_CustomTake'] input\").on(\"keypress\",onKeypressTakeNumber);\n   \tjQuery(\"[class~='eParamID_CustomClipName'] input\").on(\"keypress\",onKeypressCustomClipName);\n   \tjQuery(\"[class~='eParamID_Take'] input\").on(\"keypress\",onKeypressTakeNumber);\n   \tjQuery(\"[class~='eParamID_ClipNumber'] input\").on(\"keypress\",onKeypressClipNumber);\n\n   \tdocument.observe(\"aja:pre_page_navigate\", prePageNavigateGangPage);\t\n\t\n\tauto_refresh = false; // by default, must click available kiPros link with ctrl key depressed to toggle\n\t//new PeriodicalExecuter(auto_refresh_device_listing, 15); // update the view via timer\n\n\t// defect #4841 - 'Master' label in Transport remains after Factory Reset (HACK HACK HACK)\n\tsetTimeout(initialize_master_header_listeners, 5000);\n});\n\n\n\t</script>\n\t-->\n</head>\n\n<body>\n\t<script type=\"text/javascript\"> var product_name_text = \"HELO\";</script>\n\n\t<div class=\"outer-layout-north ui-layout-north\">\n\t\n\t\t<div class=\"headermain cion\">\n\t\t\t<img class=\"logo_right\" src=\"/images/aja_logo.svg\" alt=\"AJA\" height=\"40\"/>\n\t\n\t\n\t\t\t<img class=\"logo_centered\" src=\"/images/helo.svg\" alt=\"HELO\" height=\"40\"/>\n\t\n\t\n\t\t\t<!--\t<div class=\"headermain_text\"> HELO Status</div> -->\n\t\t</div>\n\t</div> <!-- ui-layout-north -->\n\n\t<div class=\"ui-layout-west\">\n\t\t\t<div id=\"left_nav\" class=\"aja_window\">\n\t<div class=\"window_header\">\n\t\t<span class=\"window_header_label\">Menu</span>\n\t\t<span class=\"window_header_button\"></span>\n\t</div>\n\t<div class=\"window_content left_nav_menu\">\n\t\t<a id=\"status_page_link\" class=\"link_button link_button_selected\" onClick=\"nav_page('status_page');\" href=\"javascript: void(0);\">Status</a>\n\t\t<a id=\"config_page_link\" class=\"link_button\" onClick=\"nav_page('config_page');\" href=\"javascript: void(0);\">Config</a>\n\t\t<a id=\"scheduler_page_link\" class=\"link_button\" onClick=\"nav_page('scheduler_page');\" href=\"javascript: void(0);\">Scheduler</a>\n\t\t<a id=\"recording_profiles_page_link\"  class=\"link_button\" onClick=\"nav_page('recording_profiles_page');\" href=\"javascript: void(0);\">Recording Profiles</a>\n\t\t<a id=\"streaming_profiles_page_link\"  class=\"link_button\" onClick=\"nav_page('streaming_profiles_page');\" href=\"javascript: void(0);\">Streaming Profiles</a>\n\t\t<!-- <a id=\"gang_page_link\" class=\"link_button\" onClick=\"nav_page('gang_page');\" href=\"javascript: void(0);\">Gang</a> -->\n\t\t<a id=\"presets_page_link\" class=\"link_button\" onClick=\"nav_page('presets_page');\" href=\"javascript: void(0);\">Presets</a>\n\t\t<a class=\"link_button event_network_window\" href=\"javascript: void(0);\">Network</a>\n\t\t<a id=\"firmware_page_link\" class=\"link_button\" onClick=\"nav_page('firmware_page');\" href=\"javascript: void(0);\">Firmware</a>\n\t\t<!--<a class=\"link_button\" href=\"firmware.tmpl\">Firmware</a> -->\n\t</div>\n</div>\n\n\t\t\t<div id=\"media_window\" class=\"aja_window\">\n\t<div class=\"window_header\">\n\n\t\t<span class=\"window_header_label\">Recordings</span>\n\n\t\t<span class=\"window_header_button\"></span>\n\n\n\t</div>\n\t<div class=\"window_content playlists\"><span style=\"font-size: .8em;\">Scanning...</span>\n\t</div>\n</div>\n\n<div id=\"add_playlist_dialog\" style=\"display: none;\">\n\t<label for=\"new_playlist_name\">Name:&nbsp;</label>\n\t<input id=\"new_playlist_name\"/>\n</div>\n\n\n\t\t\t<div id=\"alarm_status\" class=\"aja_window\">\n\t\t<div class=\"window_header\">\n\t\t\t<span class=\"window_header_label\">Alarms</span>\n\t\t\t<span class=\"window_header_button\"></span>\n\n\t\t</div>\n\t\t<table id=\"alarm_control_table\" class=\"window_content alarm_control readonly\" cellspacing=\"6px\">\n\t\t\t<tr id=\"no_alarms\">\n\t\t\t\t<td id=\"eAlarmNone\" class=\"alarm_none\">None</td>\n\t\t\t</tr>\n\t\t</table>\n\t\t<!-- <div class=\"window_content\" style=\"display: none;\"><span class=\"alarm_flash on_collapsed_flash\" style=\"display: none;\">Alarms Exist</span></div> -->\n</div>\n\n\t</div> <!-- ui-layout-west -->\n\n\t<div class=\"outer-layout-center ui-layout-center\" style=\"overflow: hidden;\">\n\t\t<div class=\"monitor\" style=\"height: 100%;\">\n\t\t\t<div class=\"middle-north ui-layout-north\">\n\t\t\t\t<!-- main content body -->\n<div class=\"main_content control_page\" style=\"\">\n\t<div class=\"transport_status_header expand\" >\n\t\t<span class=\"eParamID_SystemName\">HELO </span>\n\t\t<div class=\"encode-small\">\n\t\t\t<span class=\"record\" title=\"Toggle Recording\"></span>\n\t\t\t<span class=\"link\" title=\"Toggle Linked Streams\"></span>\n\t\t\t<span class=\"stream\" title=\"Toggle Streaming\"></span>\n\t\t</div>\n\t\t<div class=\"busy_indicator\">\n\t\t\t<div class=\"busy_circle_1 circle\"></div>\n\t\t\t<div class=\"busy_circle_2 circle\"></div>\n\t\t\t<div class=\"busy_circle_3 circle\"></div>\n\t\t</div>\n\t\t<div class=\"media-state gray\" title=\"Toggle Data Transfer mode.  Currently OFF\"><span></span></div>\n\t\t<div class=\"scheduler-state\" title=\"Current status of scheduler.\"></div>\n\t\t<div class=\"codec-state\" title=\"Current status of linked codec settings.\"></div>\n\t\t<div class=\"lock-state\" title=\"Current status of front panel button lock\"></div>\n\t\t<span class=\"window_header_button\" title=\"Click to toggle...\"></span>\n\t</div>\n\t\n\t<div id=\"slider\" class=\"\">\n\t\t<div class=\"preview-control\">\n\t\t\t<label>Monitor</label>\n\t\t</div>\n\t\t\n\t\t<div class=\"encode-controls\">\n\t\t\t<div class=\"encode-control encode-record\">\n\t\t\t\t<label>Record</label>\n\t\t\t\t<div class=\"button record\" title=\"Toggle Recording\"></div>\n\t\t\t\t<select class=\"record-profile\"></select>\n\t\t\t\t<div class=\"view-settings\" title=\"View Record Profile Settings\"></div>\n\t\t\t</div>\n\t\t\t<div class=\"encode-control encode-linker\">\n\t\t\t\t<label>Link</label>\n\t\t\t\t<div class=\"button link\" title=\"Toggle linked streaming and recording stop and start buttons.\"></div>\n\t\t\t\t<div id=\"shared-codec-settings\" class=\"aja-dropdown-widget\" title='Selects whether record and stream have separate codec settings, or the stream codec settings are also used for recording.  For high resolution, high frame rate simultaneous recording and streaming, such as dual 1080p60, this must be set to \"Use Stream Codec For Record.\"'>\n\t\t\t\t\t<span class=\"current-value\">Initializing</span>\n\t\t\t\t\t<span class=\"window_header_button\"></span>\n\t\t\t\t\t<ul class=\"dropdown\">\n\t\t\t\t\t\t<li value=\"0\" title=\"Use separate encoding parameters for recording and streaming\">Independent Codecs</li>\n\t\t\t\t\t\t<li value=\"1\" title=\"Reuse record encoding parameters for streaming\">Use Stream Codec For Record</li>\n\t\t\t\t\t</ul>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class=\"encode-control encode-stream\">\n\t\t\t\t<label>Stream</label>\n\t\t\t\t<div class=\"button stream\" title=\"Toggle Streaming\"></div>\n\t\t\t\t<select class=\"stream-profile\"></select>\n\t\t\t\t<div class=\"view-settings\" title=\"View Stream Profile Settings\"></div>\n\t\t\t</div>\n\t\t</div>\n\t</div><!-- slider -->\n</div><!-- /main content body   -->\n\n<link href=\"/css/control.css\" rel=\"stylesheet\" type=\"text/css\" />\n\n<script>\n\t\n\t// Initialize on page load.\n\tjQuery(function() {\n\t\tjQuery.subscribe('aja:config:loaded', function(event, config) {\n\t\t\tnew EmbedControls(mapping_config);\n\t\t});\n\t});\n\t\n</script>\n\n\t\t\t</div>\n\t\t\t\n\t\t\t<div id=\"center-main\" class=\"middle-center ui-layout-center\">\n\t\t\t\n\t\t\t\t<div class=\"main_content status_page loading\">\n\t\t\t\t\t<div id=\"status_page\" class=\"wrapper_class\">\n\n\t\t\t\t\t\t<table class=\"page_table status_table status_item\">\n\t<tbody>\n\t\t<tr>\n\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t<table class=\"status_table_divider aja_window\">\n\t\t\t\t\t<thead><tr>\n\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Input Status\n\t\t\t\t\t\t<span class=\"window_header_button\"></span>\n\t\t\t\t\t</td></tr></thead>\n\t\t\t\t\t<tbody class=\"window_content\">\n\t\t\t\t\t<tr></tr>\n\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Video Input</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_VideoInSelect\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_VideoInSelect param_element \"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_VideoInSelect_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Input Format</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_DetectInputFormat\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_DetectInputFormat param_element\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_DetectInputFormat_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Audio Input</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_AudioInSelect\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_AudioInSelect param_element\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_AudioInSelect_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr> \n\t\t\t\t\t</tbody>\n\t\t\t\t</table>\n\t\t\t</td>\n\t\t</tr>\n\n\t\t<tr class=\"network_status\" style=\"\">\n\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t<table class=\"status_table_divider aja_window\">\n\t\t\t\t\t<thead><tr>\n\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Scheduler Status\n\t\t\t\t\t\t<span class=\"window_header_button\"></span>\n\t\t\t\t\t</td></tr></thead>\n\t\t\t\t\t<tbody class=\"window_content\">\n\t\t\t\t\t\t<tr></tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Scheduler</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SchedulerEnabled\" \n\t\t\t\t\t\t\t\t\t data-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_SchedulerEnabled param_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_SchedulerEnabled_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Current Event</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SchedulerCurrentEvent\" \n\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_SchedulerCurrentEvent string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_SchedulerCurrentEvent_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Next Event</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SchedulerNextEvent\" \n\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_SchedulerNextEvent string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_SchedulerNextEvent_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Scheduled Activities</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SchedulerActivity\" \n\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_SchedulerActivity string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_SchedulerActivity_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Calendar Source</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SchedulerSource\" \n\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_SchedulerSource string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_SchedulerSource_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr id=\"eParamID_SchedulerRemoteURL_status_page_row\">\n\t\t\t\t\t\t\t<td class=\"label\">Remote Calender URL</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t <div data-paramid=\"eParamID_SchedulerRemoteURL\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_SchedulerRemoteURL string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_SchedulerRemoteURL_status_page\"></div></td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr id=\"eParamID_SchedulerLastRemoteSync_status_page_row\">\n\t\t\t\t\t\t\t<td class=\"label\">Remote Calendar Sync</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SchedulerLastRemoteSync\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_SchedulerLastRemoteSync string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_SchedulerLastRemoteSync_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr id=\"eParamID_SchedulerLocalCalendarFile_status_page_row\">\n\t\t\t\t\t\t\t<td class=\"label\">Local Calendar File</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SchedulerLocalCalendarFile\"\n\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_SchedulerLocalCalendarFile string_element readonly\"\n\t\t\t\t\t\t\t\t\t id=\"eParamID_SchedulerLocalCalendarFile_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t</tbody>\n\t\t\t\t</table>\n\t\t\t</td>\n\t\t</tr>\n\t\t<tr class=\"network_status\" style=\"\">\n\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t<table class=\"status_table_divider aja_window\">\n\t\t\t\t\t<thead><tr>\n\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Streaming Session\n\t\t\t\t\t\t\t<span class=\"window_header_button\"></span>\n\t\t\t\t\t\t</td></tr></thead>\n\t\t\t\t\t<tbody class=\"window_content\">\n\t\t\t\t\t<tr></tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Streaming Profile</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_StreamingProfileName\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_StreamingProfileName string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_StreamingProfileName_status_page\"></div></td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Stream URI</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_Encoder2StreamURI\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_Encoder2StreamURI param_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_Encoder2StreamURI_status_page\"></div></td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Streaming Duration</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_StreamingDuration\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_StreamingDuration string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_StreamingDuration_status_page\"></div></td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Streaming Format</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_StreamingFormat\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_StreamingFormat string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_StreamingFormat_status_page\"> </div></td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t</tbody>\n\t\t\t\t</table>\n\t\t\t</td>\n\t\t</tr>\n\t\t<tr class=\"network_status\" style=\"\">\n\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t<table class=\"status_table_divider aja_window\">\n\t\t\t\t\t<thead>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">\n\t\t\t\t\t\t\t\tRecording Session\n\t\t\t\t\t\t\t\t<span class=\"window_header_button\"></span>\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t</thead>\n\t\t\t\t\t<tbody class=\"window_content\">\n\t\t\t\t\t\t<tr></tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Max Recording Length</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_RecordingDurationExtent\" \n\t\t\t\t\t\t\t\t\t data-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_RecordingDurationExtent param_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_RecordingDurationExtent_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t<!-- TODO: If extent == fixed, show fixed duration -->\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Recording Duration</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_RecordingDuration\" \n\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_RecordingDuration string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_RecordingDuration_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Recording Filename</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_RecordingFilename\" \n\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_RecordingFilename string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_RecordingFilename_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">File Duration</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_FileDuration\" \n\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t class=\"eParamID_FileDuration string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_FileDuration_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Recording Format</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t <span data-paramid=\"eParamID_RecordingFormat\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_RecordingFormat string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_RecordingFormat_status_page\"></span> <!--\n\t\t\t\t\t\t\t\t<span data-paramid=\"eParamID_CameraSlowMotion\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_CameraSlowMotion param_element\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_CameraSlowMotion_status_page\" style=\"display: inline;\"></span>&nbsp;\n\t\t\t\t\t\t\t\t<span data-paramid=\"eParamID_EncodeType\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_EncodeType param_element\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_EncodeType_status_page\" style=\"display: inline;\"></span> -->\n\t\t\t\t\t\t\t\t<div id=\"rec_status_page\"> </div>\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<!-- Hide timecode for NAB 2016\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Timecode</td>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_DisplayTimecode\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_DisplayTimecode string_element readonly\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_DisplayTimecode_status_page\"></div></td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t-->\n\t\t\t\t\t\t<tr class=\"dual_status_block\">\n\t\t\t\t\t\t\t<td colspan=\"3\">\n\t\t\t\t\t\t\t\t<table class=\"status_table_divider aja_window primary\">\n\t\t\t\t\t\t\t\t\t<thead>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">\n\t\t\t\t\t\t\t\t\t\t\t\tRecording Volume\n\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t</thead>\n\t\t\t\t\t\t\t\t\t<tbody class=\"window_content\">\n\t\t\t\t\t\t\t\t\t\t<tr></tr>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">Recording State</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_ReplicatorPrimaryRecState\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t data-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t class=\"eParamID_ReplicatorPrimaryRecState param_element\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t id=\"eParamID_ReplicatorPrimaryRecState_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">Recording Destination</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_RecordingDestination\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t data-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t class=\"eParamID_RecordingDestination param_element\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t id=\"eParamID_RecordingDestination_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t<tr class=\"eParamID_RecordingShareNetworkCIFS enabled_by active param_disabled\">\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">CIFS Destination</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_RecordingShareNetworkCIFS\" \n\t\t\t\t\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t class=\"eParamID_RecordingShareNetworkCIFS param_element enabled_by readonly\" \n\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"eParamID_RecordingShareNetworkCIFS_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t<tr class=\"eParamID_RecordingShareNetworkNFS enabled_by active param_disabled\">\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">NFS Destination</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_RecordingShareNetworkNFS\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t class=\"eParamID_RecordingShareNetworkNFS param_element enabled_by readonly\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t id=\"eParamID_RecordingShareNetworkNFS_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">Storage Volume Name</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_VolumeName\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tdata-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"eParamID_VolumeName string_element readonly\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"eParamID_VolumeName_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">Capacity Remaining</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_CurrentMediaAvailable\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tdata-paramtype=\"integer\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"eParamID_CurrentMediaAvailable integer_element readonly\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"eParamID_CurrentMediaAvailable_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t<table class=\"status_table_divider aja_window secondary\">\n\t\t\t\t\t\t\t\t\t<thead>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">\n\t\t\t\t\t\t\t\t\t\t\t\tSecondary Recording Volume\n\t\t\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t</thead>\n\t\t\t\t\t\t\t\t\t<tbody class=\"window_content\">\n\t\t\t\t\t\t\t\t\t\t<tr></tr>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">Recording State</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_ReplicatorSecondaryRecState\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t data-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t class=\"eParamID_ReplicatorSecondaryRecState param_element\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t id=\"eParamID_ReplicatorSecondaryRecState_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">Recording Destination</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SecondaryRecordingDestination\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t data-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t class=\"eParamID_SecondaryRecordingDestination param_element\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t id=\"eParamID_SecondaryRecordingDestination_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t<!-- Network is not supported as a secondary destination \n\t\t\t\t\t\t\t\t\t\t<tr class=\"eParamID_SecondaryRecordingShareNetworkCIFS enabled_by active param_disabled\">\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">CIFS Destination 2</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SecondaryRecordingShareNetworkCIFS\" \n\t\t\t\t\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t class=\"eParamID_SecondaryRecordingShareNetworkCIFS param_element enabled_by readonly\" \n\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"eParamID_SecondaryRecordingShareNetworkCIFS_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t<tr class=\"eParamID_SecondaryRecordingShareNetworkNFS enabled_by active param_disabled\">\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">NFS Destination 2</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SecondaryRecordingShareNetworkNFS\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t class=\"eParamID_SecondaryRecordingShareNetworkNFS param_element enabled_by readonly\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t id=\"eParamID_SecondaryRecordingShareNetworkNFS_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t-->\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">Storage Volume Name</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SecondaryVolumeName\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tdata-paramtype=\"string\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"eParamID_SecondaryVolumeName string_element readonly\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"eParamID_SecondaryVolumeName_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"label\">Capacity Remaining</td>\n\t\t\t\t\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SecondaryMediaAvailable\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tdata-paramtype=\"integer\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tclass=\"eParamID_SecondaryMediaAvailable integer_element readonly\"\n\t\t\t\t\t\t\t\t\t\t\t\t\tid=\"eParamID_SecondaryMediaAvailable_status_page\"></div></td>\n\t\t\t\t\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t</tbody>\n\t\t\t\t</table>\n\t\t\t</td>\n\t\t</tr>\n\t\t\n\t\t<tr class=\"network_status\" style=\"\">\n\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t<table class=\"status_table_divider aja_window\">\n\t\t\t\t\t<thead><tr>\n\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Network Status\n\t\t\t\t\t\t<span class=\"window_header_button\"></span>\n\t\t\t\t\t</td></tr></thead>\n\t\t\t\t\t<tbody class=\"window_content\">\n\t\t\t\t\t<tr></tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">IP&nbsp;Address Type</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_IPConfig\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_IPConfig string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_IPConfig_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">IP&nbsp;Address</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_IPAddress_Actual\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_IPAddress_Actual string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_IPAddress_Actual_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Netmask</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_SubnetMask_Actual\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_SubnetMask_Actual string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_SubnetMask_Actual_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Default Gateway</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_DefaultGateway_Actual\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_DefaultGateway_Actual string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_DefaultGateway_Actual_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Primary DNS Server</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_DNS1_Actual\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_DNS1_Actual string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_DNS1_Actual_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Secondary DNS Server</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_DNS2_Actual\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_DNS2_Actual string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_DNS2_Actual_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">DNS Search Path</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_DNS_Search_Actual\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_DNS_Search_Actual string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_DNS_Search_Actual_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">MAC&nbsp;Address</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_MACAddress\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_MACAddress string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_MACAddress_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Link State</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_LinkState\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_LinkState string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_LinkState_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t</tbody>\n\t\t\t\t</table>\n\t\t\t</td>\n\t\t</tr>\n\t\t<tr class=\"network_status\" style=\"\">\n\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t<table class=\"status_table_divider aja_window\">\n\t\t\t\t\t<thead><tr>\n\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Firmware Status\n\t\t\t\t\t\t<span class=\"window_header_button\"></span>\n\t\t\t\t\t\t</td></tr></thead>\n\t\t\t\t\t<tbody class=\"window_content\">\n\t\t\t\t\t<tr></tr>\n\t\t\t\t\t<tr>\n\t\t\t\t\t\t<td class=\"label\">Installed</td>\n\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t<div data-paramid=\"eParamID_CurrentlyRunningImageVersion\"\n\t\t\t\t\t\t\t\t data-paramtype=\"string\"\n\t\t\t\t\t\t\t\t class=\"eParamID_CurrentlyRunningImageVersion string_element readonly\"\n\t\t\t\t\t\t\t\t id=\"eParamID_CurrentlyRunningImageVersion_status_page\"></div></td>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t\t<td></td>\n\t\t\t\t\t</tr>\n\t\t\t\t\t</tbody>\n\t\t\t\t</table>\n\t\t\t</td>\n\t\t</tr>\n\t\t<tr class=\"interval_status\" style=\"display:none;\">\n\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t<table class=\"status_table_divider\">\n\t\t\t\t\t<tbody>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Interval&nbsp;Status</td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"label\">Interval&nbsp;Frames</td>\n\t\t\t\t\t\t\t<td>Capture\n\t\t\t\t\t\t\t\t<span data-paramid=\"eParamID_IntervalFrames\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_IntervalFrames param_element\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_IntervalFrames_status_page\">\n\t\t\t\t\t\t\t\t</span>&nbsp;frame(s)&nbsp;each&nbsp;<span data-paramid=\"eParamID_IntervalTime\"\n\t\t\t\t\t\t\t\t\tdata-paramtype=\"enum\"\n\t\t\t\t\t\t\t\t\tclass=\"eParamID_IntervalTime param_element\"\n\t\t\t\t\t\t\t\t\tid=\"eParamID_IntervalTime_status_page\">\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t<td></td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t</tbody>\n\t\t\t\t</table>\n\t\t\t</td>\n\t\t</tr>\n\t</tbody>\n</table>\n\n\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t\n\t\t\t\t<div class=\"main_content config_page loading\">\n\t\t\t\t\t<div id=\"config_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\t\t\t\t<table class=\"page_table\">\n\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t<table class=\"status_table_divider\">\n\t\t\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Config Page</td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t</table>\n\t\t\t\t\t\t<table class=\"config_page page_table\"></table>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\n\t\t\t\t<div class=\"main_content scheduler_page loading\">\n\t\t\t\t\t<div id=\"scheduler_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\t\t\t\t<table class=\"page_table\">\n\t<tbody>\n\t\t<tr>\n\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t<div class=\"scheduler_divider page_table aja_window\">\n\t\t\t\t\t<div class=\"scheduler_divider label window_header\">\n\t\t\t\t\t\t<span class=\"window_header_label\" style=\"vertical-align: middle;\">Scheduler Control</span>\n\t\t\t\t\t\t<span class=\"window_header_button\"></span>\n\t\t\t\t\t</div>\n\t\t\t\t\t<table id=\"scheduler_table\" class=\"scheduler_page page_table scheduler_control window_content\">\n\t\t\t\t\t\t<tr class=\"insert_before_me scheduler_page\" style=\"display:none;\" cellspacing=\"0px\"\n\t\t\t\t\t\tcellpadding=\"0px\"></tr>\n\t\t\t\t\t</table>\n\t\t\t\t</div>\n\t\t\t</td>\n\t\t</tr>\n\t\t<tr>\n\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t<div class=\"scheduler_divider page_table aja_window main_scheduler\">\n\t\t\t\t\t<div class=\"scheduler_divider label window_header\">\n\t\t\t\t\t\t<span class=\"window_header_label\">Calendar</span>\n\t\t\t\t\t\t<span class=\"sync_remote_calendar sync_remote_calendar_divider\" title=\"Synchronize Now\">&#x27f3;</span>\n\t\t\t\t\t\t<span style=\"position:relative\">\n\t\t\t\t\t\t\t<span class=\"window_header_button\" style=\"vertical-align: middle;\"></span>\n\t\t\t\t\t\t<button class=\"menu_button\" style=\"float: right; margin-right: 2px;\"></button>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<div class=\"scheduler_popup_menu\" style=\"display:none;\">\n\t\t\t\t\t\t\t<ul class=\"scheduler_menu\">\n\t\t\t\t\t\t\t\t<li class=\"first_leaf\">\n\t\t\t\t\t\t\t\t\t<label class=\"tipToggle\">\n\t\t\t\t\t\t\t\t\t\t<input id=\"tooltips\" type=\"checkbox\" class=\"ckbox\" />Enable Event Tooltips\n\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t<label class=\"tipToggle\">\n\t\t\t\t\t\t\t\t\t\t<input id=\"start_on_monday\" type=\"checkbox\" class=\"ckbox\" />Start Week on Monday\n\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t<label class=\"tipToggle\">\n\t\t\t\t\t\t\t\t\t\t<input id=\"use_24hr_clock\" type=\"checkbox\" class=\"ckbox\" />Use 24-hour Clock\n\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t<li>\n\t\t\t\t\t\t\t\t\t<label class=\"tipToggle\">\n\t\t\t\t\t\t\t\t\t\t<input id=\"sched_kb_shcuts\" type=\"checkbox\" class=\"ckbox\" />Popup Scheduler Shortcuts\n\t\t\t\t\t\t\t\t\t</label>\n\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t<li style=\"padding-left:21px;\">\n\t\t\t\t\t\t\t\t\tLanguage:\n\t\t\t\t\t\t\t\t\t<select id=\"scheduler_language\">\n\t\t\t\t\t\t\t\t\t\t<option value=\"ar\">Arabic </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"be\">Belarus </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"ca\">Catalan </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"cn\">Chinese </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"cs\">Czech </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"da\">Danish </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"nl\">Dutch </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"en\">English </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"fi\">Finnish </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"fr\">French </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"de\">German </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"el\">Greek </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"he\">Hebrew </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"hu\">Hungarian </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"id\">Indonesian</option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"it\">Italian </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"jp\">Japanese </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"no\">Norwegian </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"pl\">Polish </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"pt\">Portuguese</option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"ro\">Romanian </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"ru\">Russian </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"si\">Slovenian </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"es\">Spanish </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"sv\">Swedish </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"tr\">Turkish </option>\n\t\t\t\t\t\t\t\t\t\t<option value=\"ua\">Ukrainian </option>\n\t\t\t\t\t\t\t\t\t</select>\n\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<table id=\"scheduler_calendar_table\" class=\"scheduler_page page_table window_content\">\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td class=\"input-row\">\n\t\t\t\t\t\t\t\t<a href=\"#\" class=\"standard_button\" style=\"float: right\" onclick=\"userInitiatedJournalClear(); return false;\">Clear History</a>\n\t\t\t\t\t\t\t\t<a id=\"download_history\" class=\"standard_button\" style=\"float:right;margin-right:5px;\" href=\"helo-scheduler-history.ics\" download=\"helo-scheduler-history.ics\" title=\"Click to download\">Download History</a>\n\t\t\t\t\t\t\t\t<a id=\"clear_local_calendar\" class=\"clear_local_calendar standard_button\" style=\"float:right;margin-right:5px;\" href=\"#\" title=\"Click to clear local calendar of future events\">Clear Calendar</a>\n\t\t\t\t\t\t\t\t<div class=\"sync_remote_container\">\n\t\t\t\t\t\t\t\t\t<a class=\"sync_remote_calendar standard_button\" href=\"#\">Sync Now</a>\n\t\t\t\t\t\t\t\t\tLast Remote Sync:\n\t\t\t\t\t\t\t\t\t<span id=\"last-synced\" class=\"static_text non_mapped_param enabled_by param_enabled param_element eParamID_SchedulerLastRemoteSync\">Loading ... </span>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t<td>\n\t\t\t\t\t\t\t\t<div id=\"scheduler_here\" class=\"dhx_cal_container\" style=\"min-width:815px; max-height: 743px;\">\n\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_navline\">\n\t\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_prev_button\">&nbsp;</div>\n\t\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_next_button\">&nbsp;</div>\n\t\t\t\t\t\t\t\t\t\t<div class=\"dhx_minical_icon\" id=\"dhx_minical_icon\" onclick=\"show_minical()\">&nbsp;</div>\n\t\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_today_button\"></div>\n\t\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_date\"></div>\n\t\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_tab\" name=\"day_tab\" style=\"right:204px;\"></div>\n\t\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_tab\" name=\"week_tab\" style=\"right:140px;\"></div>\n\t\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_tab\" name=\"month_tab\" style=\"right:76px;\"></div>\n\t\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_tab\" name=\"agenda_tab\" style=\"right:280px;\"></div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_header\"></div>\n\t\t\t\t\t\t\t\t\t<div class=\"dhx_cal_data\" style=\"max-height: 660.6px;\"></div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t</tr>\n\t\t\t\t\t</table>\n\t\t\t\t</div>\n\t\t\t</td>\n\t\t</tr>\n\t\t<!--\n\t\t<tr><td style=\"width: 100%; vertical-align: top;\"><table class=\"status_table_divider\"><thead><tr><td class=\"header label\" colspan=\"3\">Scheduler Footer\n\n\t\t\t\t\t\t</td></tr></thead><tbody class=\"three_col_content\"><tr><td>Uno</td><td>Dos</td><td>Tres</td></tr></tbody></table></td></tr>\n-->\n\t</tbody>\n</table>\n\n\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<div class=\"main_content recording_profiles_page loading\">\n\t\t\t\t\t<div id=\"recording_profiles_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\t\t\t\t<table class=\"recording_profiles_page_import_table page_table readonly_hide\">\n\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t<table class=\"status_table_divider\">\n\t\t\t\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Import Recording Settings</td>\n\t\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t</table>\n\t\t\t\t\t\t<table class=\"page_table\">\n\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t\t<table class=\"status_table_divider\">\n\t\t\t\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Recording Profile Settings</td>\n\t\t\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t</table>\n\t\t\t\t\t\t<table class=\"recording_profiles_page page_table\"></table>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<div class=\"main_content streaming_profiles_page loading\">\n\t\t\t\t\t<div id=\"streaming_profiles_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\t\t\t\t<table class=\"streaming_profiles_page_import_table page_table readonly_hide\">\n\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t<table class=\"status_table_divider\">\n\t\t\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Import Streaming Settings</td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t</table>\n\t\t\t\t\t\t<table class=\"page_table\">\n\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t<table class=\"status_table_divider\">\n\t\t\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Streaming Profile Settings</td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t</table>\n\t\t\t\t\t\t<table class=\"streaming_profiles_page page_table\"></table>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t\n\t\t\t\t<div class=\"main_content destinations_page loading\">\n\t\t\t\t\t<div id=\"destinations_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\t\t\t\t<table class=\"destinations_page page_table\"></table>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\n\t\t\t\t<!--\n\t\t\t\t<div class=\"main_content gang_page loading\">\n\t\t\t\t\t<div id=\"gang_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\t\t\t\n\t\t\t\t\t\t<div id=\"gang_view\" class=\"gang_panel\">\n\t\t\t\t\t\t\n\t\t\t\t\t\t\t<div id=\"master_header\">&nbsp;</div>\n\t\t\t\t\t\t\t<div id=\"gang_refresh_button\" class=\"gang_refresh_button\">Refresh</div>\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t<table class=\"gang sortable\">\n\t\t\t\t\t\t\t<thead>\n\t\t\t\t\t\t\t\t<tr><th>System</th><th>Record&nbsp;Format</th><th>Free&nbsp;Storage</th><th class=\"slave\" style=\"width: 15px;\">Gang</th><th class=\"master\" style=\"width: 15px;\">Master</th><th>Settings</th></tr>\n\t\t\t\t\t\t\t\t<tr><td></td><td></td><td></td><td valign=\"top\" align=\"center\"><a onclick=\"javascript:select_gang(); return false;\">All</a>/<a onclick=\"javascript:clear_gang(); return false;\">Clear</a></td><td valign=\"top\" align=\"center\"><a onclick=\"javascript:clear_master(); return false;\">Clear</a></td><td></td></tr>\n\t\t\t\t\t\t\t</thead>\n\t\t\t\t\t\t\t<tfoot>\n\t\t\t\t\t\t\t\t<tr><td></td><td></td><td></td><td valign=\"top\" align=\"center\"><a onclick=\"javascript:select_gang(); return false;\">All</a>/<a onclick=\"javascript:clear_gang(); return false;\">Clear</a></td><td valign=\"top\" align=\"center\"><a onclick=\"javascript:clear_master(); return false;\">Clear</a></td><td></td></tr>\n\t\t\t\t\t\t\t</tfoot>\n\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t-->\n\t\t\t\t\n\t\t\t\t\n\t\t\t\t<div class=\"main_content presets_page loading\">\n\t\t\t\t\t<div id=\"presets_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\t\t\t\t<table class=\"page_table\">\n\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t<table class=\"status_table_divider\">\n\t\t\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Presets Page</td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t</table>\n\t\t\t\t\t\t<table class=\"presets_page page_table\">\n\t\t\t\t\t\t\t<tr id=\"eParamID_RegisterRecall\">\n\t<td>\n\t\t<table class=\"preset_widget\">\n\t\t\t<tbody>\n\t\t\t\t<!-- Special row for 'Factory Preset' -->\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell preset_label_cell\">Factory&nbsp;Reset</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"preset_name_cell readonly\" style=\"text-align: center;\">Factory&nbsp;Settings</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">&nbsp;--&nbsp;</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">\n\t\t\t\t\t\t<span class=\"preset_button_group recall_preset\">\n\t\t\t\t\t\t\t<a class=\"preset_recall_button preset_0\">Reset</a>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">&nbsp;--&nbsp;</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">&nbsp;--&nbsp;</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">&nbsp;--&nbsp;</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"preset_status_column\">\n\t\t\t\t\t\t<span style=\"visibility:hidden;\" class=\"throbber preset_0\">\n\t\t\t\t\t\t\t<img src=\"/images/throbber.gif\" alt=\"Working...\"/>&nbsp;\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span class=\"preset_status_text string_param preset_0 eParamID_RegisterRecall\">\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</td>\n\t\t\t\t</tr>\n\t\t\t</tbody>\n\t\t</table>\n\t</td>\n</tr>\n\n\n<tr class=\"param_row insert_before_me presets_page\" id=\"eParamID_PresetExportAll\">\n\t<td>\n\t\t<table class=\"preset_widget\">\n\t\t\t<tbody>\n\t\t\t\t<!-- Special row for 'All Presets' -->\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell preset_label_cell\">Presets&nbsp;#1-20</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div c2022/02/19 18:32:57 fetch data from fofa: 1/1 
lass=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"preset_name_cell\" style=\"text-align: center;\">All</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">&nbsp;--&nbsp;</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">&nbsp;--&nbsp;</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">\n\t\t\t\t\t\t<span class=\"preset_button_group preset_all\">\n\t\t\t\t\t\t\t<a class=\"preset_export_all_button\">Export</a>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">\n\t\t\t\t\t\t<span class=\"preset_button_group preset_all\">\n\t\t\t\t\t\t\t<a class=\"preset_import_all_button\">Import</a>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell preset_menu_cell\">\n\t\t\t\t\t\t<span class=\"preset_button_group preset_all\">\n\t\t\t\t\t\t\t<a class=\"preset_erase_all_button\">Erase</a>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"preset_status_column\">\n\t\t\t\t\t\t<span style=\"visibility:hidden;\" class=\"throbber preset_all\">\n\t\t\t\t\t\t\t<img src=\"/images/throbber.gif\" alt=\"Working...\" />&nbsp;\n\t\t\t\t\t\t</span>\n\t\t\t\t\t\t<span class=\"preset_status_text string_param preset_all eParamID_RegisterExportResult eParamID_RegisterImportResult eParamID_RegisterEraseResult\">\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</td>\n\t\t\t\t</tr>\n\t\t\t</tbody>\n\t\t</table>\n\t</td>\n</tr>\n\n\n\n\t\t\t\t\t\t</table>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class=\"progress_dialog\" style=\"display: none;\">\n\t\t\t\t\t\t<div id=\"eParamID_RegisterSaveResult\" class=\"string_param preset_status\"></div>\n\t\t\t\t\t</div>\n\t\t\n\t\t\t\t\t<div class=\"progress_dialog\" style=\"display: none;\">\n\t\t\t\t\t\t<div id=\"eParamID_RegisterRecallResult\" class=\"string_param preset_status_text\"></div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<div class=\"main_content firmware_page loading\">\n\t\t\t\t\t<div id=\"firmware_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\t\t\t\t<table class=\"page_table\">\n\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t<td style=\"width: 100%; vertical-align: top;\">\n\t\t\t\t\t\t\t\t\t<table class=\"status_table_divider\">\n\t\t\t\t\t\t\t\t\t\t<tbody>\n\t\t\t\t\t\t\t\t\t\t<tr>\n\t\t\t\t\t\t\t\t\t\t\t<td class=\"header label\" colspan=\"3\">Firmware Page</td>\n\t\t\t\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t\t\t\t</table>\n\t\t\t\t\t\t\t\t</td>\n\t\t\t\t\t\t\t</tr>\n\t\t\t\t\t\t\t</tbody>\n\t\t\t\t\t\t</table>\n\t\t\t\t\t\t<table class=\"firmware_page page_table\" >\n\t\t\t\t\t\t\t<tr>\n\t<td>\n\t\t<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\">\n\t\t\t<tr>\n\t\t\t\t<td id=\"center_column\">\n\t\t\t\t\t<div class=\"content\">\n\t\t\t\t\t\t<div class=\"input-fullwidth\">\n\t\t\t\t\t\t\t<div class=\"input-row \">\n\t\t\t\t\t\t\t\t<label for=\"installed-version\">Installed:</label>\n\t\t\t\t\t\t\t\t<span id=\"installed-version\"\n\t\t\t\t\t\t\t\t\t  class=\"static_text eParamID_CurrentlyRunningImageVersion\"></span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"input-row drop update\">\n\t\t\t\t\t\t\t\t<form class=\"upload\">\n\t\t\t\t\t\t\t\t\t<label for=\"update-btn\">Upload New Firmware:</label>\n\t\t\t\t\t\t\t\t\t<input id=\"update-btn\" type=\"file\" accept=\".ajas,.*\"/>\n\t\t\t\t\t\t\t\t</form>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"input-row\">\n\t\t\t\t\t\t\t\t<label id=\"progress-label\" class=\"commit\">Update Progress:</label>\n\t\t\t\t\t\t\t\t<div id=\"progressBar\" class=\"commit\"><span class=\"progressBarLabel\">&nbsp;</span></div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div id=\"firmware_upload_aborted_message\" class=\"input-row\" style=\"display:none;\">\n\t\t\t\t\t\t\t\t<label for=\"installed-version\">Warning:</label>\n\t\t\t\t\t\t\t\t<span class=\"static_text\" style=\"color:#ff0000;\">Firmware Upgrade Aborted</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"information-row\">\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\tUse the following to perform a reboot of your HELO:\n\t\t\t\t\t\t\t\t\t<a class=\"page_link\" href=\"#\" onclick=\"userInitiatedReboot(); return false;\">Reboot</a>\n\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"information-row\">\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\tUse the following to abort an update of your HELO during the upload stage only:\n\t\t\t\t\t\t\t\t\t<a class=\"page_link\" href=\"#\" onclick=\"userInitiatedAbortUpdate(); return false;\">Abort Update</a>\n\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class=\"information-row\">\n\t\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t\tFor latest firmware point your browser to:\n\t\t\t\t\t\t\t\t\t<a class=\"page_link\" target=\"_blank\" href=\"https://www.aja.com/products/helo\">HELO Product Page</a>\n\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div><!--full-width -->\n\t\t\t\t\t</div><!--content -->\n\t\t\t\t</td>\n\t\t\t</tr>\n\t\t</table>\n\t</td>\n</tr>\n\n<link href=\"/css/firmware.css\" rel=\"stylesheet\" type=\"text/css\"/> \n\n\t\t\t\t\t\t</table>\t\t\t\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class=\"main_content clips_page\">\n\t\t\t\t\t<div id=\"clips_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\t\t\t\t<!-- see: https://github.com/blueimp/jQuery-File-Upload -->\n\n<div id=\"demo\">\n\t<form id=\"fileupload\" action=\"/media\" method=\"PUT\">\n\t\t<div class=\"row fileupload-buttonbar fileupload fg-toolbar ui-toolbar ui-widget-header ui-corner-tl ui-corner-tr ui-helper-clearfix\">\n\t\t\t<div class=\"span7\">\n\t\t\t\t<!-- The fileinput-button span is used to style the file input field as button -->\n\t\t\t\t<span class=\"btn btn-success fileinput-button\">\n\t\t\t\t\t<i class=\"icon-plus icon-white\"></i>\n\t\t\t\t\t<span>Upload...</span>\n\t\t\t\t\t<input type=\"file\" name=\"files[]\" multiple>\n\t\t\t\t</span>\n\t\t\t\t<label class=\"fileinput-button\">\n\t\t\t\t\t<span>Download...</span>\n\t\t\t\t\t<input type=\"button\" title=\"Download selected clips\" class=\"download_button\" name=\"\" />\n\t\t\t\t</label>\n\t\t\t\t<input type=\"checkbox\" class=\"toggle\" style=\"display: none;\"> <!-- required for some weird reason... -->\n\t\t\t\t<label class=\"fileinput-button\">\n\t\t\t\t\t<span>Delete Partial Uploads...</span>\n\t\t\t\t\t<input type=\"button\" title=\"Free disk space used by partial uploads\" class=\"erase_uploads_button\" name=\"\" />\n\t\t\t\t</label>\n\t\t\t\t<input type=\"checkbox\" class=\"toggle\" style=\"display: none;\"> <!-- required for some weird reason... -->\n\t\t\t</div>\n\t\t</div>\n\t\t<div class=\"fileupload-content\">\n\t\t\t<table role=\"presentation\" class=\"table table-striped\">\n\t\t\t\t<tbody class=\"files\" data-toggle=\"modal-gallery\" data-target=\"#modal-gallery\"></tbody>\n\t\t\t</table>\n\t\t\t<div class=\"fileupload-progressbar\"></div>\n\t\n\t\t\t<div class=\"span5 fileupload-progress\">\n\t\t\t\t<!-- fade The global progress bar -->\n\t\t\t\t<div class=\"progress progress-success progress-striped active\"\n\t\t\t\t\trole=\"progressbar\" aria-valuemin=\"0\" aria-valuemax=\"100\">\n\t\t\t\t\t<div class=\"bar\" style=\"width: 0%;\"></div>\n\t\t\t\t</div>\n\t\t\t\t<!-- The extended global progress information -->\n\t\t\t\t<div class=\"progress-extended\">&nbsp;</div>\n\t\t\t</div>\n\t\t</div>\n\t</form>\n</div>\n\n<!-- see scripts in nav.js for how/hide logic for eParamID_MediaState -->\n<!-- The template to display files available for upload -->\n<script id=\"template-upload\" type=\"text/x-tmpl\">\n{% for (var i=0, file; file=o.files[i]; i++) { %}\n    <tr class=\"template-upload fade\">\n        <td class=\"preview\"><span class=\"fade\"></span></td>\n        <td class=\"name\"><span>{%=file.name%}</span></td>\n        <td class=\"size\"><span>{%=o.formatFileSize(file.size)%}</span></td>\n        {% if (file.error) { %}\n            <td class=\"error\" colspan=\"2\"><span class=\"label label-important\">Error</span> {%=file.error%}</td>\n        {% } else if (o.files.valid && !i) { %}\n            <td>\n                <div class=\"progress progress-success progress-striped active\" role=\"progressbar\" aria-valuemin=\"0\" aria-valuemax=\"100\" aria-valuenow=\"0\"><div class=\"bar\" style=\"width:0%;\"></div></div>\n            </td>\n            <td class=\"start\">{% if (!o.options.autoUpload) { %}\n                <button class=\"btn btn-primary\">\n                    <i class=\"icon-upload icon-white\"></i>\n                    <span>Start</span>\n                </button>\n            {% } %}</td>\n        {% } else { %}\n            <td colspan=\"2\"></td>\n        {% } %}\n        <td class=\"cancel\">{% if (!i) { %}\n            <button class=\"btn btn-warning\">\n                <i class=\"icon-ban-circle icon-white\"></i>\n                <span>Cancel</span>\n            </button>\n        {% } %}</td>\n    </tr>\n{% } %}\n</script>\n<!-- The template to display files available for download -->\n<script id=\"template-download\" type=\"text/x-tmpl\">\n{% for (var i=0, file; file=o.files[i]; i++) { %}\n    <tr class=\"template-download fade\">\n        {% if (file.error) { %}\n            <td></td>\n            <td class=\"name\"><span>{%=file.name%}</span></td>\n            <td class=\"size\"><span>{%=o.formatFileSize(file.size)%}</span></td>\n            <td class=\"error\" colspan=\"2\"><span class=\"label label-important\">Error</span> {%=file.error%}</td>\n        {% } else { %}\n            <td class=\"preview\">{% if (file.thumbnail_url) { %}\n                <a href=\"{%=file.url%}\" title=\"{%=file.name%}\" rel=\"gallery\" download=\"{%=file.name%}\"><img src=\"{%=file.thumbnail_url%}\"></a>\n            {% } %}</td>\n            <td class=\"name\">\n                <a href=\"{%=file.url%}\" title=\"{%=file.name%}\" rel=\"{%=file.thumbnail_url&&'gallery'%}\" download=\"{%=file.name%}\">{%=file.name%}</a>\n            </td>\n            <td class=\"size\"><span>{%=o.formatFileSize(file.size)%}</span></td>\n            <td colspan=\"2\"></td>\n        {% } %}\n        <td class=\"delete\">\n            <button class=\"btn btn-danger\" data-type=\"{%=file.delete_type%}\" data-url=\"{%=file.delete_url%}\">\n                <i class=\"icon-trash icon-white\"></i>\n                <span>Delete</span>\n            </button>\n            <input type=\"checkbox\" name=\"delete\" value=\"1\">\n        </td>\n    </tr>\n{% } %}\n</script>\n\n<script id=\"template-uploadx\" type=\"text/x-jquery-tmpl\">\n    <tr class=\"template-upload{{if error}} ui-state-error{{/if}}\">\n        <!-- NOTE: not supported <td class=\"preview\"></td> -->\n        <td class=\"name\">${name}</td>\n        <td class=\"size\">${sizef}</td>\n        {{if error}}\n            <td class=\"error\" colspan=\"2\">Error:\n                {{if error === 'maxFileSize'}}File is too big\n                {{else error === 'minFileSize'}}File is too small\n                {{else error === 'acceptFileTypes'}}Filetype not allowed\n                {{else error === 'maxNumberOfFiles'}}Max number of files exceeded\n                {{else}}${error}\n                {{/if}}\n            </td>\n        {{else}}\n            <td class=\"progress\"><div></div></td>\n            <!-- we are using auto start <td class=\"start\"><button>Start</button></td> -->\n        {{/if}}\n        <td class=\"cancel\"><button>Cancel</button></td>\n    </tr>\n</script>\n\n<script id=\"template-downloadx\" type=\"text/x-jquery-tmpl\">\n    <tr class=\"template-download{{if error}} ui-state-error{{/if}}\">\n        {{if error}}\n            <td></td>\n            <td class=\"name\">${name}</td>\n            <td class=\"size\">${sizef}</td>\n            <td class=\"error\" colspan=\"2\">Error:\n                {{if error === 1}}File exceeds upload_max_filesize (php.ini directive)\n                {{else error === 2}}File exceeds MAX_FILE_SIZE (HTML form directive)\n                {{else error === 3}}File was only partially uploaded\n                {{else error === 4}}No File was uploaded\n                {{else error === 5}}Missing a temporary folder\n                {{else error === 6}}Failed to write file to disk\n                {{else error === 7}}File upload stopped by extension\n                {{else error === 'maxFileSize'}}File is too big\n                {{else error === 'minFileSize'}}File is too small\n                {{else error === 'acceptFileTypes'}}Filetype not allowed\n                {{else error === 'maxNumberOfFiles'}}Max number of files exceeded\n                {{else error === 'uploadedBytes'}}Uploaded bytes exceed file size\n                {{else error === 'emptyResult'}}Empty file upload result\n                {{else}}${error}\n                {{/if}}\n            </td>\n        {{else}}\n\t\t\t<!-- NOTE: not supported\n            <td class=\"preview\">\n                {{if thumbnail_url}}\n                    <a href=\"${url}\" target=\"_blank\"><img src=\"${thumbnail_url}\"></a>\n                {{/if}}\n            </td>\n\t\t\t-->\n            <td class=\"name\">\n                <a href=\"${url}\"{{if thumbnail_url}} target=\"_blank\"{{/if}}>${name}</a>\n            </td>\n            <td colspan=\"3\">Upload complete</td>\n        {{/if}}\n        <td class=\"cancel\"><button>Close</button></td>\n    </tr>\n</script>\n\n\t\t\t\t\t\t<table class=\"clips_page clips_table\">\t\n\t\t\t\t\t\t</table>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t\n\t\t\t\t<div class=\"main_content alarm_config_page loading\">\n\t\t\t\t\t<div id=\"alarm_config_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t\t\n\t\t\t\t\t</div>\n\t\t\t\t</div>\t\n\t\t\t\t\n\t\t\t\t<!-- NETWORK PAGE -->\n\t\t\t\t<div class=\"main_content network_page loading\">\n\t\t\t\t\t<!-- network_page wrapper_class -->\n<!-- When adding controls to the network page, be sure to add corresponding code to webd's handler_networking.cpp -->\n<div id=\"network_page\" class=\"wrapper_class\" style=\"display: none;\">\n\t<table class=\"network_page page_table\">\t\t\t\n\t\t<tr class=\"insert_before_me network_page\">\n\t\t\t<td>\n\t\t\t\t<table class=\"three_col_content\">\n\t\t\t\t\t<tbody><tr>\n\t\t\t\t\t\t<td class=\"param_name_cell param_name\">IP Address Type</td>\n\t\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t\t<td class=\"content_cell param_control\">\n\t\t\t\t\t\t\t<select id=\"iptype_toggle\" title=\"Toggle the type of IP Address to use\">\n\t\t\t\t\t\t\t\t<option name=\"DHCP\" value=\"dhcp\">DHCP</option>\n\t\t\t\t\t\t\t\t<option name=\"Static\" value=\"static\">Static</option>\n\t\t\t\t\t\t\t</select>\n\t\t\t\t\t\t</td>\n\t\t\t\t\t</tr></tbody>\t\n\t\t\t\t</table>\n\t\t\t</td>\n\t\t</tr>\t\n\t</table>\t\t\t\n\t<div id=\"iptype_dhcp\">\n\t<form action=\"/network/update\" method=\"POST\">\n\n\t\t<input name=\"new_ip_type\" type=\"hidden\" value=\"1\"/>\n\t\t<table class=\"page_table\"><tr><td>\n\t\t\t<table class=\"three_col_content\">\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">IP Address</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell\"><input name=\"new_ip\" type=\"text\" data-paramid=\"eParamID_IPAddress_Actual\" class=\"IP_control readonly eParamID_IPAddress_Actual\" disabled=\"disabled\" value=\"To be determined...\" /></td>\n\t\t\t\t</tr>\n\t\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Netmask</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_mask\" type=\"text\" data-paramid=\"eParamID_SubnetMask_Actual\" class=\"IP_control readonly eParamID_SubnetMask_Actual\" disabled=\"disabled\" value=\"To be determined...\" /></td>\n\t\t\t\t</tr>\n\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Default Gateway</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_gw\" type=\"text\" data-paramid=\"eParamID_DefaultGateway_Actual\" class=\"IP_control readonly eParamID_DefaultGateway_Actual\" disabled=\"disabled\" value=\"To be determined...\" /></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Primary DNS Server</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_dns1\" type=\"text\" data-paramid=\"eParamID_DNS1_Actual\" class=\"IP_control readonly eParamID_DNS1_Actual\" disabled=\"disabled\" value=\"To be determined...\" /></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Secondary DNS Server</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_dns2\" type=\"text\" data-paramid=\"eParamID_DNS2_Actual\" class=\"IP_control readonly eParamID_DNS2_Actual\" disabled=\"disabled\" value=\"To be determined...\" /></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">DNS Search Path</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_dns_search\" type=\"text\" data-paramid=\"eParamID_DNS_Search_Actual\" class=\"IP_control readonly eParamID_DNS_Search_Actual\" disabled=\"disabled\" value=\"To be determined...\" /></td>\n\t\t\t\t</tr>\n\t\t\t\t<!-- Note: there is no Apply button - this is a read-only UI to allow the user to see the automatically configured DHCP address -->\n\t\t\t</table>\n\t\t</td></tr></table>\t\n\t\t\t\n\t</form>\n\t</div>\n\n\t<div id=\"iptype_dhcp_pending\">\n\t<form action=\"/network/update\" method=\"POST\">\n\n\t\t<input name=\"new_ip_type\" type=\"hidden\" value=\"1\"/>\n\t\t<table class=\"page_table\"><tr><td>\n\t\t\t<table class=\"three_col_content\">\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">IP Address</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell\">To be determined by DHCP</td>\n\t\t\t\t</tr>\n\t\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Netmask</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\">To be determined by DHCP</td>\n\t\t\t\t</tr>\n\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Default Gateway</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\">To be determined by DHCP</td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Primary DNS Server</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\">To be determined by DHCP</td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Secondary DNS Server</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\">To be determined by DHCP</td>\n\t\t\t\t</tr>\t\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">DNS Search Path</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\">To be determined by DHCP</td>\n\t\t\t\t</tr>\t\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell\"></td>\n\t\t\t\t\t<td class=\"separator_cell\"></td>\t\t\t\n\t\t\t\t\t<td class=\"content_cell param_control\"><input type=\"button\" value=\"Apply\" class=\"network_apply_button\"/></td>\n\t\t\t\t</tr>\n\t\t\t\t\n\t\t\t</table>\n\t\t</td></tr></table>\t\n\t\t\t\n\t</form>\n\t</div>\n\n\t<div id=\"iptype_static\" style=\"display: none;\">\n\t<form action=\"/network/update\" method=\"POST\">\n\n\t\t<input name=\"new_ip_type\" type=\"hidden\" value=\"2\"/>\n\t\t\n\t\t<table class=\"page_table\" cellspacing=\"0px\" cellpadding=\"0px\"><tr><td>\n\t\t\t<table class=\"three_col_content\">\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">IP Address</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell\"><input name=\"new_ip\" type=\"text\" data-paramid=\"eParamID_IPAddress\" class=\"IP_control eParamID_IPAddress\" /></td>\n\t\t\t\t</tr>\n\t\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Netmask</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_mask\" type=\"text\" data-paramid=\"eParamID_SubnetMask\" class=\"IP_control eParamID_SubnetMask\" /></td>\n\t\t\t\t</tr>\n\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Default Gateway</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_gw\" type=\"text\" data-paramid=\"eParamID_DefaultGateway\" class=\"IP_control eParamID_DefaultGateway\" /></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Primary DNS Server</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_dns1\" type=\"text\" data-paramid=\"eParamID_DNS1\" class=\"IP_control eParamID_DNS1\"/></td>\n\t\t\t\t</tr>\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">Secondary DNS Server</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_dns2\" type=\"text\" data-paramid=\"eParamID_DNS2\" class=\"IP_control eParamID_DNS2\"/></td>\n\t\t\t\t</tr>\n\t\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell param_name\">DNS Search Path</td>\n\t\t\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input name=\"new_dns_search\" type=\"text\" data-paramid=\"eParamID_DNS_Search\" class=\"IP_control eParamID_DNS_Search\"/></td>\n\t\t\t\t</tr>\n\t\t\t\t\n\t\t\t\t<tr>\n\t\t\t\t\t<td class=\"param_name_cell\"></td>\n\t\t\t\t\t<td class=\"separator_cell\"></td>\n\t\t\t\t\t<td class=\"content_cell param_control\"><input type=\"button\" value=\"Apply\" class=\"network_apply_button\"/></td>\n\t\t\t\t</tr>\n\t\t\t</table>\n\t\t</td></tr></table>\t\n\n\t</form>\n\t</div>\n\n\t<table class=\"three_col_content\">\n\t\t<tr>\n\t\t\t<td class=\"param_name_cell param_name\">MAC Address</td>\n\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t<td class=\"content_cell param_control\"><input name=\"mac_addr\" type=\"text\" data-paramid=\"eParamID_MACAddress\" class=\"IP_control readonly eParamID_MACAddress\" disabled=\"disabled\" value=\"\"/></td>\n\t\t</tr>\n\t\t<tr>\n\t\t\t<td class=\"param_name_cell param_name\">Link State</td>\n\t\t\t<td class=\"separator_cell\"><div class=\"separator_div\"></div></td>\n\t\t\t<td class=\"content_cell param_control\"><input name=\"link_state\" type=\"text\" data-paramid=\"eParamID_LinkState\" class=\"IP_control readonly eParamID_LinkState\" disabled=\"disabled\" value=\"\"/></td>\n\t\t</tr>\n\t</table>\n\t\t\n\t\n</div>\n<!-- /network_page wrapper_class -->\n\n<script type=\"text/javascript\">\n\nfunction validate_controls()\n{\n\t\n\ttry {\n\t\tvar ip_type_local = GetLocalIPConfig();\t\t\t// \"dhcp\" or \"static\"\n\t\tvar ip_type_current = GetCurrentIPConfig();\t\t// \"dhcp\" or \"static\"\n\t\t\n\t\tvar ip = null;\n\t\tvar mask = null;\n\t\tvar dns1 = null;\n\t\tvar dns2 = null;\n\t\tvar dns_search = null;\n\n\t\tvar parent = jQuery(\"#iptype_\"+ip_type_local);\n\t\tvar gw = jQuery(\"[name=new_gw]\",parent).val();\n\t\t\n\t\tif (ip_type_local == \"dhcp\" && ip_type_local != ip_type_current) {\n\t\t\t// leave as null... ip   = \"IP Address: To be determined by DHCP\";\n\t\t\t// leave as null... mask = \"Netmask: To be determined by DHCP\";\n\t\t\t// NOTE: we don't validate ip or netmask if this is DHCP\n\t\t} else {\n\t\t\tip = jQuery(\"[name=new_ip]\",parent).val(); \n\t\t\tmask = jQuery(\"[name=new_mask]\",parent).val();\n\t\t\tdns1 = jQuery(\"[name=new_dns1]\",parent).val();\n\t\t\tdns2 = jQuery(\"[name=new_dns2]\",parent).val();\n\t\t\tdns_search = jQuery(\"[name=new_dns_search]\",parent).val();\n\n\t\t\tif (!isValidIPAddress(ip)) {\n\t\t\t\tmodal_alert(\"The IP Address \" + ip + \" is invalid.\");\n\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\n\t\t\tif (!isValidNetmask(mask)) {\n\t\t\t\tmodal_alert(\"The Netmask \" + mask + \" is invalid.\");\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t//DNS acceptable: none, one, both set\n\t\t\tif(dns1){\n\t\t\t\tif(dns2){\n\t\t\t\t\tif (!isValidDNSAddress(dns2)) {\n\t\t\t\t\t\tmodal_alert(\"The Secondary DNS \" + dns2 + \" is invalid.\");\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t\tif(dns1 === dns2){\n\t\t\t\t\t\tmodal_alert(\"The primary DNS \" + dns1 + \" and the secondary DNS \"+ dns2 +\" cannot have the same address.\");\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(!isValidDNSAddress(dns1)) {\n\t\t\t\t\t\tmodal_alert(\"The Primary DNS \" + dns1 + \" is invalid.\");\n\t\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}else{\n\t\t\t\tif(dns2){\n\t\t\t\t\tif (!isValidDNSAddress(dns2)) {\n\t\t\t\t\t\tmodal_alert(\"The Secondary DNS \" + dns2 + \" is invalid.\");\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t}\n\t\t// always validate gw, and allow an empty gateway.\n\t\tif (gw !== \"\" && !isValidIPAddress(gw)) {\n\t\t\tmodal_alert(\"The Default Gateway Address \" + gw + \" is invalid.\");\n\t\t\treturn false;\n\t\t}\n\t    return true;\n\t} catch (e) {\n\t\tdebug(\"Caught exception: \" + e.name + \", \" + e.message );\n\t}\n\n\treturn false;\n}\n\nfunction GetLocalIPConfig() {\n\tvar ip_type = jQuery(\"#iptype_toggle\").val();\n\treturn ip_type;\n}\n\nfunction GetCurrentIPConfig() {\n\tvar ip_type;\n\tvar ipconfig = +mapping_config.get(\"eParamID_IPConfig\");\n\tif (ipconfig == eIPConfigDHCP) {\n\t\tip_type = \"dhcp\";\n\t}\n\telse {\n\t\tip_type = \"static\";\n\t}\n\treturn ip_type;\n}\n\nfunction IsDHCPPending() {\n\tif (GetLocalIPConfig() == \"dhcp\" && GetCurrentIPConfig() != \"dhcp\")\n\t{\n\t\treturn true; // local UI shows dhcp but config isn't dhcp\n\t}\n\treturn false;\n}\n\nfunction confirmUpdate(element) {\n\n\tif (mapping_config.isLocalOnly()) {\n\t\tmodal_alert(config.REMOTE_ONLY_ERROR_MESSAGE);\n\t\treturn;\n\t}\n\n\tif (!validate_controls()) {\n\t\treturn;\n\t}\n\t\n\tvar submit_button = element;\n\tvar iptype = jQuery(\"#iptype_toggle\").val();\n\tvar parent = jQuery(\"#iptype_\"+iptype);\n\tvar ip = jQuery(\"[name=new_ip]\",parent).val(); \n\tvar mask = jQuery(\"[name=new_mask]\",parent).val();\n\tvar gateway = jQuery(\"[name=new_gw]\",parent).val();\n\tvar dns1 = jQuery(\"[name=new_dns1]\",parent).val();\n\tvar dns2 = jQuery(\"[name=new_dns2]\",parent).val();\n\tvar dns_search = jQuery(\"[name=new_dns_search]\",parent).val();\n\n\tif (iptype == \"static\") {\n\t\tiptype = \"Static\";\n\t} else {\n\t\tiptype = \"DHCP\";\n\t}\n\t\n\tvar ip_settings = \"\";\n\tif (iptype != \"DHCP\")\n\t{\n\t\tip_settings\n\t\t\t+= \"<br/>\" + \"IP Address:&nbsp;\" + ip \n\t\t\t+  \"<br/>\" + \"Netmask:&nbsp;\" + mask\n\t\t\t+  \"<br/>\" + \"Gateway:&nbsp;\" + gateway;\n\t\tip_settings+=  \"<br/>\" + \"Primary DNS:&nbsp;\";\n\t\tif(dns1)\n\t\t\tip_settings+= dns1;\n\t\tip_settings+=  \"<br/>\" + \"Primary DNS:&nbsp;\";\n\t\tif(dns2)\n\t\t\tip_settings+= dns2;\n\t\tip_settings+=  \"<br/>\" + \"DNS Search Path:&nbsp;\";\n\t\tif(dns_search)\n\t\t\tip_settings+= dns_search;\n\t}\n\t\n\tvar title = \"Network Change Confirmation\";\n\tvar message = \"Update the \" + config.product_name + \" to the following:<br/><br/>\" +\n\t   \"Address Type : \" + iptype\n\t   + ip_settings;\n\tvar confirm_button_text = \"Confirm\";\n\t\n\tmodal_confirm(title, message, confirm_button_text, function (result) {\n\t\tif (result == true) {\n\t\t\tjQuery(submit_button).closest(\"form\").submit();\n\t\t} else {\n\t\t\t// close dialog?\n\t\t}\n\t});\n}\n\nfunction update_ip_controls() {\n\tvar iptype = jQuery(\"#iptype_toggle\").val();\n/*\tif (iptype == \"default\") {\n\t\tjQuery(\"#iptype_default\").show();\n\t\tjQuery(\"#iptype_dhcp\").hide();\n\t\tjQuery(\"#iptype_static\").hide();\n\t\tjQuery(\"#iptype_dhcp_pending\").hide();\n\t} else\n*/\t\n\t if (iptype == \"dhcp\") {\n\t\tif (IsDHCPPending()) {\n\t\t\t//jQuery(\"#iptype_default\").hide();\t\t\n\t\t\tjQuery(\"#iptype_dhcp\").hide();\n\t\t\tjQuery(\"#iptype_static\").hide();\n\t\t\tjQuery(\"#iptype_dhcp_pending\").show();\n\t\t} else {\n\t\t\t//jQuery(\"#iptype_default\").hide();\t\t\n\t\t\tjQuery(\"#iptype_dhcp\").show();\n\t\t\tjQuery(\"#iptype_static\").hide();\n\t\t\tjQuery(\"#iptype_dhcp_pending\").hide();\n\t\t}\n\t}\n\telse {\n\t\t//jQuery(\"#iptype_default\").hide();\t\t\n\t\tjQuery(\"#iptype_dhcp\").hide();\n\t\tjQuery(\"#iptype_static\").show();\t\n\t\tjQuery(\"#iptype_dhcp_pending\").hide();\n\t}\n}\n\nfunction on_ip_config_change()\n{\n\tjQuery(\".network_apply_button\").removeAttr(\"disabled\");\n\tupdate_ip_controls();\n\tvar iptype = jQuery(\"#iptype_toggle\").val();\n\tvar parent = jQuery(\"#iptype_\"+iptype);\n\tjQuery(\"[name=new_ip]\",parent).focus();\n}\n\n\n//\n//RegExps for validation\n//\n\n//Address\n//keep - var regex = /\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b/\nvar regex = /\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b/\nvar ADDRESS_REGEX = new RegExp(regex);\n\n//Special Addresses\n//regex = /\\b(22[4-9]|23[0-9]|24[0-9]|25[0-5])\\b/\nregex = /\\b((22[4-9]|23[0-9]|24[0-9]|25[0-5])\\.)(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){2}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b/\nvar SPECIAL_ADDRESS_REGEX = new RegExp(regex);\n\n//Netmask\n// Replace original netmask regex which allowed ***********.  Bug #12557\n//regex = /\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b/\nregex = /(254|252|248|240|224|192|128)\\.0\\.0\\.0|255\\.(254|252|248|240|224|192|128|0)\\.0\\.0|255\\.255\\.(254|252|248|240|224|192|128|0)\\.0|255\\.255\\.255\\.(254|252|248|240|224|192|128|0)/\nvar NETMASK_REGEX = new RegExp(regex);\n\n//Special Netmasks\nregex = /(254|255)$/\nvar SPECIAL_NETMASK_REGEX = new RegExp(regex);\n\n//keypresses (valid input characters)\nregex = /[0-9]|[\\.]/\nvar KEYPRESS_FILTER_REGEX = new RegExp(regex);\n\n//dns\nregex = /^(?![0-9]+$)(?!-)[a-zA-Z0-9%-]{,63}(?!-)$/i;\nvar DNS_REGEX = new RegExp(regex);\n\nvar auto_refresh = false; // by default, you must click Available FS1s link with ctrl key depressed to toggle\n\nfunction isValidIPAddress(value)\n{\n\ttry {\n\t\tif (!value || value == null) {\n\t\t\treturn false;\n\t\t}\n\t\t\n\t\tif (value.match(ADDRESS_REGEX)) {\n\t\t\tif (!value.match(SPECIAL_ADDRESS_REGEX)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t}\n\t\t}\n\t} catch(e) {\n\t}\n\treturn false;\n}\n\n\nfunction isValidNetmask(value)\n{\n\ttry {\n\t\tif (!value || value == null) {\n\t\t\treturn false;\n\t\t}\n\t\t\n\t\tif (value.match(NETMASK_REGEX)) {\n\t\t\tif (!value.match(SPECIAL_NETMASK_REGEX)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t}\n\t\t}\n\t} catch(e) {\n\t}\n\treturn false;\n}\nfunction isValidDNSAddress(value){\n\n\ttry {\n\t\tif (!value || value == null) {\n\t\t\treturn false;\n\t\t}\n\t\tif (value.match(ADDRESS_REGEX)) {\n\t\t\tif (!value.match(SPECIAL_ADDRESS_REGEX)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t}\n\t\t}\n\t} catch(e) {\n\t}\n\treturn false;\n}\n\nfunction isValidIPKeypress(value)\n{\n\ttry {\n\t\tif (!(KEYPRESS_FILTER_REGEX.test(value))) {\n\t\t\treturn false;\n\t\t} else {\n\t\t\tjQuery(\".network_apply_button\").removeAttr(\"disabled\");\n\t\t\treturn true;\n\t\t}\n\t} catch(e) {\n\t}\n\t\n\treturn false;\n}\n\nfunction onIPConfigChanged(event) {\n\tdebug(\"onIPConfigChanged called\");\n\tdebug(\"onIPConfigChanged - event.memo.int_value = \" + event.memo.int_value);\n\tvar ip_type;\n\tvar ipconfig = event.memo.int_value;\n\tif (ipconfig == eIPConfigDHCP) {\n\t\tip_type = \"dhcp\";\n\t}\n\telse {\n\t\tip_type = \"static\";\n\t}\n\tjQuery(\"#iptype_toggle\").val(iptype);\n\tupdate_ip_controls();\n\n}\n\njQuery(function() {\n\tjQuery.subscribe('aja:config:loaded', function(event, config) {\n\n\tvar elements = jQuery(\".IP_control\");\n\telements.each( function(index, element) {\n\t\tvar param_id = jQuery(element).attr(\"data-paramid\");\n\t\tif (param_id.indexOf(\"MACAddress\") != -1) {\n\t\t\t\telement.value = config.get_str_value(\"eParamID_MACAddress\");\n\t\t\t}\n\t\telse if (param_id.indexOf(\"LinkState\") != -1) {\n\t\t\t\telement.value = config.get_str_value(\"eParamID_LinkState\");\n\t\t\t}\n\t\telse if (param_id.indexOf(\"DNS_Search_Actual\") != -1) {\n\t\t\t\telement.value = config.get_str_value(\"eParamID_DNS_Search_Actual\");\n\t\t\t} \n\t\telse if (param_id.endsWith(\"DNS_Search\")) {\n\t\t\t\telement.value = config.get_str_value(\"eParamID_DNS_Search\");\n\t\t\t}\n\t\t});\n\t});\n});\n\ndocument.observe(\"dom:loaded\",function()\n{\n\ttry {\n\t\tjQuery(\"#iptype_toggle\").change(on_ip_config_change);\n\t\tdocument.observe(\"aja:config_event:eParamID_IPConfig\", onIPConfigChanged);\n\n\t\tvar elements = jQuery(\".IP_control\");\n\t\telements.each( function(index, element) {\n\t\t\tvar param_id = jQuery(element).attr(\"data-paramid\");\n\t\t\tif (param_id.indexOf(\"IPAddress\") != -1) {\n\t    \t\t\tnew IPStringControl(element,isValidIPKeypress,isValidIPAddress,param_id); // attach our class to each element, with callbacks for validation\n\t\t\t} else if (param_id.indexOf(\"SubnetMask\") != -1) {\n\t    \t\t\tnew IPStringControl(element,isValidIPKeypress,isValidNetmask,param_id); // attach our class to each element, with callbacks for validation\n\t\t\t} else if (param_id.indexOf(\"DefaultGateway\") != -1) {\n\t\t\t\t\tnew IPStringControl(element,isValidIPKeypress,isValidIPAddress,param_id);\n\t\t\t} else if (param_id.indexOf(\"DNS1\") != -1) {\n\t\t\t\t\tnew IPStringControl(element,isValidIPKeypress,isValidIPAddress,param_id);\n\t\t\t} else if (param_id.indexOf(\"DNS2\") != -1) {\n\t\t\t\t\tnew IPStringControl(element,isValidIPKeypress,isValidIPAddress,param_id);\n\t\t\t} else if (param_id.endsWith(\"DNS_Search\")) {\n\t\t\t\t\tnew TextControl(element);\n\t\t\t}\n\t\t}); // each\n\t\t\n\t\t// At this point iptype_toggle has not even been set yet.\n\t\t// It will be set in the callback of the jQuery.ajax call below.\n\t\t//update_ip_controls();\n\t} catch (e) { \n\t      debug(\"Caught exception: \" + e.name + \", \" + e.message );\n\t}\n\t\n\tvar url = '/config?action=get&paramid=eParamID_IPConfig';\n\tjQuery.ajax({\n\t\turl: url,\n\t\tdataType:\"json\",\n\t\tsuccess: function(data,resultText,xhr) {\n\t\t\ttry {\n\t\t\t\t//data = data.trim();\n\t\t\t\t//data = '(' + data + ')';\n\t\t\t\t//var ipconfig = jQuery.parseJSON(data);\n\t\t\t\tvar ipconfig = data;\n\t\t\t\t/*if (ipconfig.value == eIPConfigDefault) {\n\t\t\t\t\tjQuery(\"#iptype_toggle\").val(\"default\");\n\t\t\t\t} else\n\t\t\t\t*/\n\t\t\t\t if (ipconfig.value == eIPConfigDHCP) {\n\t\t\t\t\tjQuery(\"#iptype_toggle\").val(\"dhcp\");\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tjQuery(\"#iptype_toggle\").val(\"static\");\t\t\t\t\n\t\t\t\t}\n\t\t\t\tupdate_ip_controls();\n\t\t\t} catch (e) { /*alert(e);*/ }\n\t\t}\n\t});\n\t\n\tjQuery(\".network_apply_button\").click( function (event) {\n\t\tconfirmUpdate(this);\n\t});\n\t \n}); // observe\n</script>\n\n\t\t\t\t</div>\t\t\n\t\t\t\t<!-- NETWORK PAGE -->\n\t\t\t\t\n\t\t\t\t\n\t\t\t\t\n\t\t\t</div>\n\t\t</div>\n\t\t\n\t\t\t<div id=\"transport_progress_bar\" class=\"transport_progress_bar\" style=\"display: none;\">\n\t\t\n\t\t\t<img src=\"/images/transport/horz_helo_logo_bottom.png\" alt=\"HELO\" width=\"300px\"><br/>\n\t\t\n\t\t<br/>\n\t\t<div id=\"eParamID_Progress\" class=\"text_control transport_progress_text\"></div>\n\t</div>\n\n\t<div id=\"media_progress_bar\" class=\"transport_progress_bar\" style=\"display: none;\">\n\t\t\n\t\t\t<img src=\"/images/transport/horz_helo_logo_bottom.png\" alt=\"HELO\" width=\"300px\"><br/>\n\t\t\n\t\t<br/>\n\t\t<div id=\"eParamID_MediaLoading\" class=\"text_control transport_progress_text\"></div>\n\t</div>\n\n\t<div id=\"slot_status\" class=\"transport_progress_bar\" style=\"display: none;\">\n\t\t\n\t\t\t<img src=\"/images/transport/horz_helo_logo_bottom.png\" alt=\"HELO\" width=\"300px\"><br/>\n\t\t\n\t\t<br/>\n\t\t<div class=\"transport_progress_text\">Next Slot...</div>\n\t</div>\n\n\t<div id=\"reboot_message\" class=\"transport_progress_bar\" style=\"display: none;\">\n\t\t\n\t\t\t<img src=\"/images/transport/horz_helo_logo_bottom.png\" alt=\"HELO\" width=\"300px\"><br/>\n\t\t\n\t\t<br/>\n\t\t<div class=\"message_text transport_progress_text\">WARNING:<br/>Please Reboot</div>\n\t\t<a class=\"page_link\" href=\"#\" onclick=\"mapping_config.config.set('eParamID_Reboot', eFWImageMostRecent); return false;\">Reboot Now</a>\n\t</div>\n\t\n\t<div id=\"generic_message\" class=\"transport_progress_bar\" style=\"display: none;\">\n\t\t\n\t\t\t<img src=\"/images/transport/horz_helo_logo_bottom.png\" alt=\"Ki Pro\" width=\"300px\"><br/>\n\t\t\n\t\t<br/>\n\t\t<div class=\"message_text transport_progress_text\"></div>\n\t</div>\n\n\t</div> <!-- ui-layout-center -->\n\t\n\t<div class=\"ui-layout-east\">\n\t\t<div id=\"connection_window\" class=\"aja_window\">\n\t\t<div class=\"window_header\">\n\t\t\t<span class=\"window_header_label\">Connection</span>\n\t\t\t<span class=\"window_header_button\"></span>\n\t\t</div>\n\t\t<table class=\"window_content connection_control\" cellspacing=\"4px\">\t\t\n\t\t\t<tr><td style=\"font-weight: bold; font-size:13px;\">Serial Number</td></tr>\n\t\t\t<tr><td style=\"font-style: italic; font-size:12px; padding-left:8px;\" class=\"static_text eParamID_FormattedSerialNumber\">Not Connected</td></tr>\n\t\t\t\n\t\t\t<tr><td style=\"font-weight: bold; font-size:13px;\">Software Version</td></tr>\n\t\t\t<tr><td style=\"font-style: italic; font-size:12px; padding-left:8px;\" class=\"static_text eParamID_CurrentlyRunningImageVersion\">Not Connected</td></tr>\n\t\t\t\n\t\t\t<tr><td style=\"font-weight: bold; font-size: 13px;\">Connection Status</td></tr>\n\t\t\t<tr><td style=\"font-style: italic; font-size:12px; padding-left:8px;\" id=\"connection_status\" class=\"connection_status loading\">Connecting...</td></tr>\n\t\t\t\n\t\t\t<!--\n\t\t\t\t<tr><td style=\"font-weight: bold; font-size: 13px;\">Authentication</td></tr>\n\t\t\t\t<tr><td style=\"font-style: italic; font-size:12px; padding-left:8px;\" id=\"auth_status\" class=\"static_text eParamID_Authentication\">Disabled</td></tr>\n\t\t\t-->\n\t\t</table>\n</div>\n\n\t\t<div class=\"aja_window network_window\">\n\t\t<div class=\"window_header\">\n\t\t\t<span class=\"window_header_label\">Network</span>\n\t\t\t<span class=\"window_header_button\"></span>\n\t\t\t<span class=\"network_icon window_header_action event_network_window\"></span>\n\t\t\t<!--images/settings_button.png -->\n\t\t</div>\n\t\t<div class=\"window_content\">\n\t\t</div>\n</div>\n\n<div id=\"idMenu\" class=\"contextMenu\" style=\"display: none;\">\n\t<ul>\n\t\t<li id=\"identify\">Identify</li>\n\t</ul>\n</div>\n\n\t</div> <!-- ui-layout-east -->\n\n\t<div class=\"ui-layout-south\">\n\t&nbsp;\n\t<div id=\"resetMenu\" class=\"contextMenu\" style=\"display: none;\">\n\t\t<ul>\n\t\t\t<li id=\"param_reset\" style=\"cursor: pointer;\">&nbsp;Reset&nbsp;To&nbsp;Factory</li>\n\t\t</ul>\n\t</div>\n\t</div>\n\n\t<div id=\"enumContextMenu\" class=\"contextMenu\" style=\"display: none;\">\n\t\t<ul class=\"param_enums_context_menu\"> <!-- added dynamically from descriptors via beforeShow of menu, see init_scripts.js (until it's moved elsewhere...) -->\n\t\t</ul>\n\t</div>\n\t\n\t<script type=\"text/javascript\">\n\tfunction display_message_dialog(text) {\n\t\tjQuery(\"#generic_message .message_text\").html(text);\n\t\tjQuery(\"#generic_message\").show();\n\t}\n\t\n\tfunction dismiss_message_dialog() {\n\t\tjQuery(\"#generic_message\").hide();\n\t}\n\tdocument.observe(\"aja:on_disconnect\", dismiss_message_dialog);\n\t\t\n\t</script>\n\t\n\t<div id=\"product_name\" style=\"display: none;\">AJA HELO</div>\n\n</body>\n</html>\n",
  "nhash": "4193432686411747509",
  "ehash": "4ec8b0ecaaa18ac6f9f30f407bacd1a8",
  "lastupdatetime": "2018-05-03 15:51:23"
}
`
	Load(context.Background(), testRule, true)
	obj, _ := structs.NewJsonObj(d1)
	b.Log(len(obj.StringOfField("body")))
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Products(obj)
	}

}
