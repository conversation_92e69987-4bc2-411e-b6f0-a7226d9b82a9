package rulengine

import (
	"context"
	"git.gobies.org/sutra/gosutra/structs"
	"io/ioutil"
)

/*
Products 从json中获取产品列表

obj 是已经解析后的JsonObj
*/
func Products(obj *structs.JsonObj) ([]*structs.Product, error) {
	return getProductManager().ProductsOfJson(obj)
}

/*
Load 加载规则

rulesContent 原始规则内容，json列表的格式

clearBeforeLoad 加载前是否清空，false为追加模式
*/
func Load(ctx context.Context, rulesContent string, clearBeforeLoad bool) bool {
	return getProductManager().LoadProducts(ctx, rulesContent, clearBeforeLoad)
}

func UpdateOne(ctx context.Context, rulesContent string) bool {
	return getProductManager().UpdateProduct(rulesContent)
}

func DeleteOne(ctx context.Context, rulesContent string) bool {
	return getProductManager().DelProduct(rulesContent)
}

/*
LoadFromFile 从文件加载规则

fileName 规则文件路径，文件为json列表的格式

clearBeforeLoad 加载前是否清空，false为追加模式
*/
func LoadFromFile(ctx context.Context, fileName string, clearBeforeLoad bool) (bool, error) {
	f, err := ioutil.ReadFile(fileName)
	if err != nil {
		return false, err
	}
	return getProductManager().LoadProducts(ctx, string(f), clearBeforeLoad), nil
}

func LoadFromEncrypt(ctx context.Context, rulesContent string, clearBeforeLoad bool) bool {
	return getProductManagerForEncrypt().LoadEncryptProducts(ctx, rulesContent, clearBeforeLoad)
}

/*
RuleSize 规则个数
*/
func RuleSize() int {
	return getProductManager().RuleSize()
}

/*
AllProducts 获取产品列表
*/
func AllProducts() []*structs.Product {
	return getProductManager().products
}

/*
FindProduct 获取产品信息
*/
func FindProduct(name string) *structs.Product {
	return getProductManager().Get(name)
}
