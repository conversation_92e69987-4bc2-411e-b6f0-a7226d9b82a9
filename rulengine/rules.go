package rulengine

import (
	"crypto/md5"
	"git.gobies.org/sutra/gosutra/structs"
	"log"
	"sync"
)

type ruleManager struct {
	rules    []*RuleT
	ruleSize int
	//SortRules [Service+1][]*Rule
	rm        sync.Map //确保是否存在重复的值
	debug     bool
	isEncrypt bool
}

func (r *ruleManager) ruleHash(line string) [16]byte {
	return md5.Sum([]byte(line))
}

//func (r *ruleManager) Exist(line string) bool {
//	k := r.ruleHash(line)
//	if _, ok := r.rm.Load(k); ok {
//		return true
//	}
//	return false
//}

// AddRule 添加一条规则
func (r *ruleManager) Add(name, content string) bool {
	k := r.ruleHash(name + content)
	// 确认不存在
	if _, ok := r.rm.Load(k); ok {
		return false
	}

	rule, err := NewRule(name, content)
	if err != nil {
		log.Println("[WARNING] load rule failed:", name, "err:", err)
		return false
	}
	//r.SortRules[rule.Type] = append(r.SortRules[rule.Type], rule)
	r.rules = append(r.rules, rule)
	r.ruleSize++
	r.rm.Store(k, rule)
	return true
}

func (r *ruleManager) Del(name, content string) bool {
	k := r.ruleHash(name + content)
	// 确认不存在
	if v, ok := r.rm.Load(k); !ok {
		log.Println("[WARNING] rule manager del not existed rule failed:", name, k)
		return false
	} else {
		dr := v.(*RuleT)
		index := -1
		for i, rr := range r.rules {
			if rr.id == dr.id {
				index = i
			}
		}

		if index < 0 {
			log.Println("[WARNING] rule manager del not exists:", name, dr)
			return false
		}

		r.rules = append(r.rules[:index], r.rules[index+1:]...)
		r.ruleSize--
	}

	r.rm.Delete(k)
	return true
}

func (r *ruleManager) Update(oldName, oldContent, name, content string) bool {
	oldK := r.ruleHash(oldName + oldContent)
	// 确认不存在
	if v, ok := r.rm.Load(oldK); !ok {
		log.Println("[WARNING] rule manager del not existed rule failed:", oldName, oldK)
		return false
	} else {
		dr := v.(*RuleT)
		index := -1
		for i, rr := range r.rules {
			if rr.id == dr.id {
				index = i
			}
		}

		if index < 0 {
			log.Println("[WARNING] rule manager del not exists:", name, dr)
			return false
		}

		newK := r.ruleHash(name + content)
		rule, err := NewRule(name, content)
		if err != nil {
			log.Println("[WARNING] load rule failed:", name, "err:", err)
			return false
		}
		r.rules[index] = rule
		r.rm.Delete(oldK)
		r.rm.Store(newK, rule)
		return true
	}
}

// Clear 清空
func (r *ruleManager) Clear() {
	r.rules = nil
	r.ruleSize = 0
	r.rm.Range(func(key, value interface{}) bool {
		r.rm.Delete(key)
		return true
	})
}

// Size 计数
func (r *ruleManager) Size() int {
	return r.ruleSize
}

// ProductsOfJson 标签
func (r *ruleManager) ProductsOfJson(obj *structs.JsonObj) ([]string, error) {
	var ps []string

	// 类型不符合直接跳过
	//rules := append(r.SortRules[t], r.SortRules[Mixed]...)

	for _, rule := range r.rules {
		// 之前没有匹配过
		//exists := false
		//for _, p := range ps {
		//	if p.Name == rule.Name {
		//		exists = true
		//		break
		//	}
		//}
		//if exists {
		//	continue
		//}
		if r.debug {
			log.Println("[DEBUG] process of rule:", rule.Name)
		}

		// 类型不符合直接跳过
		if rule.Type != obj.Type() && rule.Type != structs.OtMixed {
			continue
		}

		if rule.Check(obj) {
			ps = append(ps, rule.Name)
		}
	}
	return ps, nil
}

func (r *ruleManager) setDebug(v bool) {
	r.debug = v
}
