package fofa

import (
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"
)

type Fofa struct {
	PageSize      int
	FofaUrl       string
	FofaVersion   string
	FofaEmail     string
	FofaKey       string
	AdditionQuery string
	DebugLevel    int
	Full          string
	*http.Client
}

type FofaApiResults struct {
	Mode    string
	Error   bool
	Errmsg  string
	Query   string
	Page    int
	Size    int
	Results [][]string
}

// NewFofaCli通过url构建客户端，格式：<url>/?email=<email>&key=<key>&version=<v2>&tlsdisabled=false&debuglevel=0
func NewFofaCli(fofaUrl string) *Fofa {
	u, err := url.Parse(fofaUrl)
	if err != nil {
		panic(err)
	}

	debuglevel, _ := strconv.Atoi(u.Query().Get("debuglevel"))
	return newFofaCli(u.Scheme+"://"+u.Host,
		u.Query().Get("email"),
		u.Query().Get("key"),
		u.Query().Get("tlsdisabled") == "true",
		u.Query().Get("version"),
		debuglevel,
		u.Query().Get("full"),
	)
}

func newFofaCli(FofaUrl string, FofaEmail string, FofaKey string, tlsDisable bool, version string, debuglevel int, full string) *Fofa {
	if len(FofaUrl) == 0 {
		FofaUrl = "https://fofa.so"
	}
	ff := &Fofa{
		PageSize:    100, //免费的范围
		FofaUrl:     FofaUrl,
		FofaVersion: version,
		FofaEmail:   FofaEmail,
		FofaKey:     FofaKey,
		DebugLevel:  debuglevel,
		Full:        full,
		Client:      &http.Client{},
	}

	if ff.FofaVersion == "" {
		ff.FofaVersion = "v1"
	}

	if tlsDisable {
		// disable tls verify
		ff.Transport = &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
	}
	return ff
}

func (ff *Fofa) BuildQueryUrl(queryString string) string {
	return ff.FofaUrl + queryString
}

func (ff *Fofa) fetchByFields(fields string, queryString string, size int, fetchFn func(fields []string, allSize int32) bool) bool {
	page := 1
	maxSize := size
	if maxSize < 0 {
		maxSize = 10000 * 50000 //max window限制
	}
	perPage := int(math.Min(float64(maxSize), 10000))

	if ff.DebugLevel > 10 {
		log.Println(queryString)
	}

	for {
		uri := fmt.Sprintf("/api/%s/search/all?email=%s&key=%s&qbase64=%s&size=%d&page=%d&fields=%s&full=%s",
			ff.FofaVersion,
			ff.FofaEmail, ff.FofaKey,
			base64.StdEncoding.EncodeToString([]byte(queryString)),
			perPage,
			page,
			fields,
			ff.Full)
		fullUrl := ff.BuildQueryUrl(uri)

		if ff.DebugLevel > 1 {
			log.Println(fullUrl)
		}
		resp, err := ff.Get(fullUrl)
		if err != nil {
			log.Println("[WARNING] fofa query failed:", err)
			//panic(err)
			return false
		}
		defer resp.Body.Close()
		content, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			log.Println("[WARNING] fofa query failed:", err)
			//panic(err)
			return false
		}

		var v FofaApiResults
		if err := json.Unmarshal(content, &v); err != nil {
			log.Println("json Unmarshal failed: ", string(content))
			//panic(err)
			return false
		}

		if ff.DebugLevel > 1 {
			log.Printf("fetch data from fofa: %d/%d \n", len(v.Results), v.Size)
		}

		for _, hostinfo := range v.Results {
			if len(hostinfo[0]) == 0 {
				log.Println("[WARNING] no hostinfo!")
				continue //没有hostinfo
				// https://fofa.so/api/v1/search/all?email=<EMAIL>&key=xxx&qbase64=YXBwPSJIaWt2aXNpb24tQ2FtZXJhcy1hbmQtU3VydmVpbGxhbmNlIiAmJiBwcm90b2NvbCE9cnN0cA==&size=100&page=1&fields=host
			}

			if !fetchFn(hostinfo, int32(v.Size)) {
				return true
			}

			maxSize -= 1
			if maxSize == 0 {
				return true
			}
		}

		// 报错，退出
		if len(v.Errmsg) > 0 {
			log.Println(v.Errmsg)
			return false
		}

		// 没有数据，退出
		if len(v.Results) == 0 || len(v.Results) < perPage {
			return true
		}

		page += 1
	}
	return true
}

// 只提取一个字断
func (ff *Fofa) FetchSize(queryString string) (r int32) {
	ff.fetchByFields("host,ip", queryString, 1, func(fields []string, allSize int32) bool {
		r = allSize
		return false
	})
	return
}

// 获取一个随机的结果
func (ff *Fofa) FetchRaondomOne(queryString string, fields string) []string {
	var r []string

	if len(queryString) == 0 {
		queryString = "type=subdomain"
	}

	if !strings.HasPrefix(queryString, "host=") && !strings.HasPrefix(queryString, "ip=") {
		min := time.Date(2014, 11, 1, 0, 0, 0, 0, time.UTC).Unix()
		max := time.Now().Unix()
		delta := max - min
		sec := rand.Int63n(delta) + min
		ts := time.Unix(sec, 0).Format("2006-01-02 15:04:05")
		queryString = queryString + ` && before="` + ts + `"`
	}

	ff.fetchByFields(fields, queryString,
		1, func(fields []string, allSize int32) bool {
			r = fields
			return true
		})
	return r
}

type FofaApiFIDStatsResultsItem struct {
	Name  string `json:"name"`
	Count int    `json:"count"`
}

type FofaApiFIDStatsResultsList struct {
	FIDs []FofaApiFIDStatsResultsItem `json:"fid"`
}
type FofaApiFIDStatsResults struct {
	List FofaApiFIDStatsResultsList `json:"List"`
}

type FofaApiStats struct {
	Error   bool                   `json:"error"`
	IPNUm   int                    `json:"distinct_ips"`
	HashNum int                    `json:"hash_num"`
	Result  FofaApiFIDStatsResults `json:"results"`
}

// fid聚类
func (ff *Fofa) FIDStats(queryString string, debugLevel int) (*FofaApiStats, error) {
	uri := fmt.Sprintf("/api/%s/search/stats?email=%s&key=%s&qbase64=%s&size=%d&fields=fid",
		ff.FofaVersion,
		ff.FofaEmail, ff.FofaKey,
		base64.StdEncoding.EncodeToString([]byte(queryString)),
		100)
	fullUrl := ff.BuildQueryUrl(uri)

	if debugLevel > 1 {
		log.Println(fullUrl)
	}
	resp, err := ff.Get(fullUrl)
	if err != nil {
		log.Println("[WARNING] fofa query failed:", err)
		//panic(err)
		return nil, err
	}
	defer resp.Body.Close()
	content, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		log.Println("[WARNING] fofa query failed:", err)
		//panic(err)
		return nil, err
	}

	var v FofaApiStats
	if err = json.Unmarshal(content, &v); err != nil {
		log.Println("json Unmarshal failed: ", string(content))
		//panic(err)
		return nil, err
	}

	log.Printf("fetch stats from fofa: %d \n", v.HashNum)
	return &v, nil
}

// Fetch 获取原始数据
func (ff *Fofa) Fetch(queryString string, fields string, size int, fetchFn func(fields []string, allSize int32) bool) bool {
	return ff.fetchByFields(fields, queryString, size, func(fields []string, allSize int32) bool {
		return fetchFn(fields, allSize)
	})
}
